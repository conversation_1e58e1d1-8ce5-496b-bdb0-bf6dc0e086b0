{".class": "MypyFile", "_fullname": "code_preprocessor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CppCodeProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "code_preprocessor.CppCodeProcessor", "line": 2230, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "code_preprocessor.MultiLanguageCodeProcessor"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MultiLanguageCodeProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "code_preprocessor.MultiLanguageCodeProcessor", "name": "MultiLanguageCodeProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "code_preprocessor", "mro": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.__init__", "name": "__init__", "type": null}}, "_analyze_api_surface": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._analyze_api_surface", "name": "_analyze_api_surface", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_analyze_api_surface of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_analyze_code_quality": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._analyze_code_quality", "name": "_analyze_code_quality", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_analyze_code_quality of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_analyze_complexity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._analyze_complexity", "name": "_analyze_complexity", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_analyze_complexity of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_complexity_score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "complexity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._calculate_complexity_score", "name": "_calculate_complexity_score", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "complexity"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_complexity_score of MultiLanguageCodeProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_cyclomatic_complexity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._calculate_cyclomatic_complexity", "name": "_calculate_cyclomatic_complexity", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_cyclomatic_complexity of MultiLanguageCodeProcessor", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_maintainability_score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "quality"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._calculate_maintainability_score", "name": "_calculate_maintainability_score", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "quality"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_maintainability_score of MultiLanguageCodeProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_nesting_depth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._calculate_nesting_depth", "name": "_calculate_nesting_depth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_nesting_depth of MultiLanguageCodeProcessor", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_naming_convention": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._check_naming_convention", "name": "_check_naming_convention", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_naming_convention of MultiLanguageCodeProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_count_comment_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lines", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._count_comment_lines", "name": "_count_comment_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lines", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_count_comment_lines of MultiLanguageCodeProcessor", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_count_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._count_parameters", "name": "_count_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_count_parameters of MultiLanguageCodeProcessor", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_detect_code_smells": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._detect_code_smells", "name": "_detect_code_smells", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_code_smells of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_c_cpp_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "source_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_c_cpp_dependencies", "name": "_extract_c_cpp_dependencies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "source_code"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_c_cpp_dependencies of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_csharp_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "source_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_csharp_dependencies", "name": "_extract_csharp_dependencies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "source_code"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_csharp_dependencies of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_csharp_methods_from_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "class_node", "source_code", "filepath", "lang_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_csharp_methods_from_class", "name": "_extract_csharp_methods_from_class", "type": null}}, "_extract_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_dependencies", "name": "_extract_dependencies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_dependencies of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_dependencies_from_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_dependencies_from_content", "name": "_extract_dependencies_from_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_dependencies_from_content of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_documentation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_documentation", "name": "_extract_documentation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_documentation of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_methods_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "body_node", "source_code", "filepath", "lang_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_methods_from_body", "name": "_extract_methods_from_body", "type": null}}, "_extract_python_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "source_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_python_dependencies", "name": "_extract_python_dependencies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "source_code"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_python_dependencies of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_python_methods_from_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "class_node", "source_code", "filepath", "lang_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._extract_python_methods_from_class", "name": "_extract_python_methods_from_class", "type": null}}, "_generate_semantic_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._generate_semantic_tags", "name": "_generate_semantic_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_semantic_tags of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_semantic_tags_from_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._generate_semantic_tags_from_content", "name": "_generate_semantic_tags_from_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_semantic_tags_from_content of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_class_name_for_language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "class_node", "source_code", "lang_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._get_class_name_for_language", "name": "_get_class_name_for_language", "type": null}}, "_get_csharp_method_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "method_node", "source_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._get_csharp_method_name", "name": "_get_csharp_method_name", "type": null}}, "_get_function_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "func_node", "source_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._get_function_name", "name": "_get_function_name", "type": null}}, "_get_language_name_from_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ext"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._get_language_name_from_extension", "name": "_get_language_name_from_extension", "type": null}}, "_get_namespace_name_for_language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ns_node", "source_code", "lang_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._get_namespace_name_for_language", "name": "_get_namespace_name_for_language", "type": null}}, "_get_python_function_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "func_node", "source_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._get_python_function_name", "name": "_get_python_function_name", "type": null}}, "_has_conditionals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._has_conditionals", "name": "_has_conditionals", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_conditionals of MultiLanguageCodeProcessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_constants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._has_constants", "name": "_has_constants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_constants of MultiLanguageCodeProcessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_documentation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._has_documentation", "name": "_has_documentation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_documentation of MultiLanguageCodeProcessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_exception_handling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._has_exception_handling", "name": "_has_exception_handling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_exception_handling of MultiLanguageCodeProcessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_loops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._has_loops", "name": "_has_loops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_loops of MultiLanguageCodeProcessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_magic_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._has_magic_numbers", "name": "_has_magic_numbers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_magic_numbers of MultiLanguageCodeProcessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_code_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._identify_code_patterns", "name": "_identify_code_patterns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identify_code_patterns of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_semantic_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._init_semantic_patterns", "name": "_init_semantic_patterns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_semantic_patterns of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_show_enhanced_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._show_enhanced_statistics", "name": "_show_enhanced_statistics", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_show_enhanced_statistics of MultiLanguageCodeProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "additional_parsers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.additional_parsers", "name": "additional_parsers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "c_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.c_language", "name": "c_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "c_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.c_parser", "name": "c_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "complexity_thresholds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.complexity_thresholds", "name": "complexity_thresholds", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cpp_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.cpp_language", "name": "cpp_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cpp_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.cpp_parser", "name": "cpp_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_searchable_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.create_searchable_index", "name": "create_searchable_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_searchable_index of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "csharp_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.csharp_language", "name": "csharp_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "csharp_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.csharp_parser", "name": "csharp_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "export_enhanced_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "chunks", "output_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.export_enhanced_metadata", "name": "export_enhanced_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "chunks", "output_file"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export_enhanced_metadata of MultiLanguageCodeProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_classes_and_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_code", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.extract_classes_and_methods", "name": "extract_classes_and_methods", "type": null}}, "extract_csharp_basic_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.extract_csharp_basic_info", "name": "extract_csharp_basic_info", "type": null}}, "extract_enhanced_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "filepath", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.extract_enhanced_metadata", "name": "extract_enhanced_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "source_code", "filepath", "language"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_enhanced_metadata of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_functions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_code", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.extract_functions", "name": "extract_functions", "type": null}}, "extract_namespaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_code", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.extract_namespaces", "name": "extract_namespaces", "type": null}}, "extract_structs_and_typedefs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_code", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.extract_structs_and_typedefs", "name": "extract_structs_and_typedefs", "type": null}}, "extract_template_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_code", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.extract_template_definitions", "name": "extract_template_definitions", "type": null}}, "get_parser_for_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.get_parser_for_file", "name": "get_parser_for_file", "type": null}}, "go_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.go_language", "name": "go_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "go_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.go_parser", "name": "go_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "java_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.java_language", "name": "java_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "java_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.java_parser", "name": "java_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "javascript_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.javascript_language", "name": "javascript_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "javascript_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.javascript_parser", "name": "javascript_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "process_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.process_file", "name": "process_file", "type": null}}, "process_repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exclude_dirs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.process_repository", "name": "process_repository", "type": null}}, "python_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.python_language", "name": "python_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "python_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.python_parser", "name": "python_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "repo_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.repo_path", "name": "repo_path", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rust_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.rust_language", "name": "rust_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rust_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.rust_parser", "name": "rust_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "semantic_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.semantic_patterns", "name": "semantic_patterns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sql_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.sql_language", "name": "sql_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sql_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.sql_parser", "name": "sql_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "typescript_language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.typescript_language", "name": "typescript_language", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "typescript_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.typescript_parser", "name": "typescript_parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "code_preprocessor.MultiLanguageCodeProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "code_preprocessor.MultiLanguageCodeProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\code_preprocessor.py"}