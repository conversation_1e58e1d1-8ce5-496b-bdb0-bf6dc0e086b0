#!/usr/bin/env python3
"""
Script to help increase context length for Llama3 model in OpenWebUI
"""

import subprocess
import requests
import json
import time

def run_command(cmd, check=True):
    """Run shell command and return output"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.stdout.strip(), e.stderr.strip()

def check_ollama_status():
    """Check if Ollama is running and what models are available"""
    print("🔍 Checking Ollama Status...")
    
    # Check if Ollama is running
    try:
        response = requests.get("http://home-ai-server.local:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print("✅ Ollama is running")
            print(f"📋 Available models: {len(models)}")
            for model in models:
                name = model.get('name', 'unknown')
                size = model.get('size', 0) // (1024*1024*1024)  # Convert to GB
                print(f"  - {name} ({size}GB)")
            return True, models
        else:
            print("❌ Ollama not responding")
            return False, []
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        return False, []

def create_high_context_model():
    """Create a new Llama3 model with higher context length"""
    print("\n🚀 Creating High-Context Llama3 Model...")
    
    # Model configurations to try
    configs = [
        {
            'name': 'llama3-8k',
            'ctx': 8192,
            'description': 'Good for most code analysis (4-6 chunks)'
        },
        {
            'name': 'llama3-16k', 
            'ctx': 16384,
            'description': 'Excellent for complex analysis (8-12 chunks)'
        },
        {
            'name': 'llama3-32k',
            'ctx': 32768,
            'description': 'Maximum for very large codebases (15-20 chunks)'
        }
    ]
    
    print("📋 Available configurations:")
    for i, config in enumerate(configs, 1):
        print(f"  {i}. {config['name']} - {config['ctx']} tokens - {config['description']}")
    
    while True:
        try:
            choice = input("\n🎯 Choose configuration (1-3, or 'q' to quit): ").strip()
            if choice.lower() == 'q':
                return False
            
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(configs):
                selected = configs[choice_idx]
                break
            else:
                print("❌ Invalid choice. Please enter 1, 2, 3, or 'q'")
        except ValueError:
            print("❌ Invalid input. Please enter a number or 'q'")
    
    print(f"\n🔧 Creating {selected['name']} with {selected['ctx']} token context...")
    
    # Create Modelfile content
    modelfile_content = f"""FROM llama3:latest
PARAMETER num_ctx {selected['ctx']}
PARAMETER temperature 0.1
PARAMETER top_p 0.95
PARAMETER repeat_penalty 1.1
"""
    
    print("📝 Modelfile content:")
    print(modelfile_content)
    
    # Write Modelfile
    with open('Modelfile', 'w') as f:
        f.write(modelfile_content)
    
    # Create the model
    print(f"⏳ Creating model {selected['name']}... (this may take a few minutes)")
    
    cmd = f"ollama create {selected['name']} -f Modelfile"
    stdout, stderr = run_command(cmd, check=False)
    
    if "success" in stdout.lower() or stderr == "":
        print(f"✅ Successfully created {selected['name']}")
        return selected
    else:
        print(f"❌ Failed to create model:")
        print(f"stdout: {stdout}")
        print(f"stderr: {stderr}")
        return False

def test_context_length(model_name, ctx_size):
    """Test the new model's context length"""
    print(f"\n🧪 Testing {model_name} context length...")
    
    # Create a test prompt with known token count
    test_chunks = [
        "// Test code chunk 1\nfunction testFunction1() {\n    console.log('Hello World 1');\n    return true;\n}",
        "// Test code chunk 2\nfunction testFunction2() {\n    console.log('Hello World 2');\n    return false;\n}",
        "// Test code chunk 3\nclass TestClass {\n    constructor() {\n        this.value = 42;\n    }\n}",
        "// Test code chunk 4\nconst testArray = [1, 2, 3, 4, 5];\ntestArray.forEach(item => console.log(item));",
        "// Test code chunk 5\nif (condition) {\n    doSomething();\n} else {\n    doSomethingElse();\n}"
    ]
    
    # Build test prompt
    prompt = "Analyze the following code chunks:\n\n"
    for i, chunk in enumerate(test_chunks, 1):
        prompt += f"**Chunk {i}:**\n```javascript\n{chunk}\n```\n\n"
    
    prompt += "Please provide a brief analysis of each chunk."
    
    print(f"📏 Test prompt length: ~{len(prompt)} characters")
    print(f"🎯 Expected tokens: ~{len(prompt) // 4} tokens")  # Rough estimate
    
    # Test with Ollama API
    try:
        response = requests.post(
            "http://home-ai-server.local:11434/api/generate",
            json={
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "num_ctx": ctx_size
                }
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            print(f"✅ Model responded successfully!")
            print(f"📊 Response length: {len(response_text)} characters")
            print(f"🎯 Context test: PASSED")
            return True
        else:
            print(f"❌ Model test failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def check_openwebui_models():
    """Check what models are available in OpenWebUI"""
    print("\n🌐 Checking OpenWebUI Models...")
    
    try:
        # Try to get models from OpenWebUI API
        response = requests.get(
            "http://home-ai-server.local:8080/api/models",
            headers={"Authorization": "Bearer sk-320242e0335e45a4b1fa4752f758f9ab"},
            timeout=10
        )
        
        if response.status_code == 200:
            models = response.json()
            print("✅ OpenWebUI models:")
            for model in models.get('data', []):
                print(f"  - {model.get('id', 'unknown')}")
            return True
        else:
            print(f"❌ Cannot access OpenWebUI models: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking OpenWebUI: {e}")
        return False

def main():
    print("🚀 Llama3 Context Length Increase Tool")
    print("=" * 50)
    
    # Step 1: Check Ollama status
    ollama_running, models = check_ollama_status()
    if not ollama_running:
        print("\n❌ Ollama is not running. Please start Ollama first.")
        return
    
    # Check if llama3:latest exists
    llama3_exists = any('llama3:latest' in model.get('name', '') for model in models)
    if not llama3_exists:
        print("\n❌ llama3:latest not found. Please pull it first:")
        print("   ollama pull llama3:latest")
        return
    
    # Step 2: Create high-context model
    new_model = create_high_context_model()
    if not new_model:
        print("\n❌ Failed to create high-context model")
        return
    
    # Step 3: Test the new model
    test_success = test_context_length(new_model['name'], new_model['ctx'])
    if not test_success:
        print(f"\n⚠️ Model {new_model['name']} created but test failed")
    
    # Step 4: Check OpenWebUI integration
    check_openwebui_models()
    
    # Step 5: Provide next steps
    print(f"\n🎉 SUCCESS! High-context model created: {new_model['name']}")
    print("\n📋 Next Steps:")
    print("1. Go to OpenWebUI (http://home-ai-server.local:8080)")
    print("2. Select the new model in the model dropdown")
    print(f"3. The model now supports {new_model['ctx']} tokens context")
    print("4. Test with complex code analysis queries")
    print("\n💡 Benefits:")
    print(f"  - Can now use {new_model['ctx']//200} code chunks (vs 2-4 before)")
    print("  - Better context for complex analysis")
    print("  - More comprehensive code understanding")
    
    print(f"\n🔧 Model Details:")
    print(f"  Name: {new_model['name']}")
    print(f"  Context: {new_model['ctx']} tokens")
    print(f"  Temperature: 0.1 (focused responses)")
    print(f"  Top-p: 0.95 (balanced creativity)")

if __name__ == "__main__":
    main()
