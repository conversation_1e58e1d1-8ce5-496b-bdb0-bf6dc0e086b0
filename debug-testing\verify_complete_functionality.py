#!/usr/bin/env python3
"""
Comprehensive verification of all tool functionality after update
"""

import requests
import json
import time

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_tool_function(session, query, expected_indicators, test_name):
    """Test a specific tool function"""
    print(f"\n🧪 {test_name}")
    print("-" * 50)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": query}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 1000
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Response received ({len(content)} chars)")
            
            # Check for expected indicators
            found_indicators = []
            for indicator in expected_indicators:
                if indicator in content:
                    found_indicators.append(indicator)
            
            if found_indicators:
                print(f"🎉 SUCCESS: Found expected indicators: {found_indicators}")
                print(f"Preview: {content[:300]}...")
                return True
            else:
                print(f"❌ FAILED: Expected indicators not found")
                print(f"Expected: {expected_indicators}")
                print(f"Preview: {content[:300]}...")
                return False
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run comprehensive functionality tests"""
    print("🔧 Comprehensive Tool Functionality Verification")
    print("=" * 70)
    print("Testing all tool functions after update...")
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test cases with expected success indicators
    test_cases = [
        {
            "query": "select codebase utils",
            "expected": ["Selected Enhanced Codebase: utils", "Enhanced Statistics", "Documents: 479"],
            "name": "Codebase Selection"
        },
        {
            "query": "list codebases",
            "expected": ["utils", "z80emu", "modbus", "networking_project", "test_project"],
            "name": "List Codebases"
        },
        {
            "query": "get stats for utils",
            "expected": ["Documents:", "Languages:", "Files:", "utils"],
            "name": "Get Statistics"
        },
        {
            "query": "search code memory allocation",
            "expected": ["tmwmem", "tmwdiag", ".c", "malloc", "free"],
            "name": "Code Search"
        },
        {
            "query": "status",
            "expected": ["server", "status", "version", "health"],
            "name": "System Status"
        }
    ]
    
    # Run all tests
    results = []
    for test_case in test_cases:
        success = test_tool_function(
            session, 
            test_case["query"], 
            test_case["expected"], 
            test_case["name"]
        )
        results.append((test_case["name"], success))
        time.sleep(2)  # Brief pause between tests
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("The tool is working correctly with all functionality.")
    else:
        print(f"\n⚠️ {total - passed} tests failed.")
        print("The tool may need further updates or configuration.")
    
    # Additional diagnostic info
    print("\n🔍 DIAGNOSTIC INFO")
    print("-" * 30)
    print("If tests are failing:")
    print("1. Ensure the tool is updated in OpenWebUI with latest code")
    print("2. Check that the tool is enabled for llama3:latest model")
    print("3. Verify the tool ID 'code_analyzer_tools' is correct")
    print("4. Check OpenWebUI logs for any errors")

if __name__ == "__main__":
    main()
