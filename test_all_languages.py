#!/usr/bin/env python3
"""
Comprehensive test script to verify all 27 language support in the code analyzer.
Tests language detection, semantic patterns, and file processing for all supported languages.
"""

import os
import tempfile
from pathlib import Path
from code_preprocessor import MultiLanguageCodeProcessor

def create_comprehensive_test_files():
    """Create sample files for all 27 supported languages"""
    test_files = {}
    temp_dir = tempfile.mkdtemp()
    
    # Core languages (already tested)
    test_files['c'] = Path(temp_dir) / 'test.c'
    test_files['c'].write_text('int main() { printf("Hello World"); return 0; }')
    
    test_files['cpp'] = Path(temp_dir) / 'test.cpp'
    test_files['cpp'].write_text('class Test { public: void hello(); };')
    
    test_files['python'] = Path(temp_dir) / 'test.py'
    test_files['python'].write_text('def hello(): print("Hello World")')
    
    test_files['csharp'] = Path(temp_dir) / 'test.cs'
    test_files['csharp'].write_text('public class Test { public void Hello() {} }')
    
    test_files['javascript'] = Path(temp_dir) / 'test.js'
    test_files['javascript'].write_text('function hello() { console.log("Hello"); }')
    
    test_files['typescript'] = Path(temp_dir) / 'test.ts'
    test_files['typescript'].write_text('interface User { name: string; }')
    
    test_files['rust'] = Path(temp_dir) / 'test.rs'
    test_files['rust'].write_text('fn main() { println!("Hello World"); }')
    
    test_files['java'] = Path(temp_dir) / 'Test.java'
    test_files['java'].write_text('public class Test { public static void main(String[] args) {} }')
    
    test_files['go'] = Path(temp_dir) / 'test.go'
    test_files['go'].write_text('package main\nfunc main() { fmt.Println("Hello") }')
    
    test_files['sql'] = Path(temp_dir) / 'test.sql'
    test_files['sql'].write_text('SELECT * FROM users WHERE id = 1;')
    
    # Additional languages
    test_files['tcl'] = Path(temp_dir) / 'test.tcl'
    test_files['tcl'].write_text('proc hello {} { puts "Hello World" }')
    
    test_files['verilog'] = Path(temp_dir) / 'test.v'
    test_files['verilog'].write_text('module test(input clk, output reg out); always @(posedge clk) out <= 1; endmodule')
    
    test_files['bash'] = Path(temp_dir) / 'test.sh'
    test_files['bash'].write_text('#!/bin/bash\nfunction hello() { echo "Hello World"; }')
    
    test_files['commonlisp'] = Path(temp_dir) / 'test.lisp'
    test_files['commonlisp'].write_text('(defun hello () (format t "Hello World"))')
    
    test_files['elisp'] = Path(temp_dir) / 'test.el'
    test_files['elisp'].write_text('(defun hello () (message "Hello World"))')
    
    test_files['scheme'] = Path(temp_dir) / 'test.scm'
    test_files['scheme'].write_text('(define (hello) (display "Hello World"))')
    
    test_files['lua'] = Path(temp_dir) / 'test.lua'
    test_files['lua'].write_text('function hello() print("Hello World") end')
    
    test_files['make'] = Path(temp_dir) / 'Makefile'
    test_files['make'].write_text('all:\n\techo "Building..."')
    
    test_files['json'] = Path(temp_dir) / 'test.json'
    test_files['json'].write_text('{"name": "test", "version": "1.0"}')
    
    test_files['yaml'] = Path(temp_dir) / 'test.yaml'
    test_files['yaml'].write_text('name: test\nversion: 1.0\nscripts:\n  - build')
    
    test_files['xml'] = Path(temp_dir) / 'test.xml'
    test_files['xml'].write_text('<?xml version="1.0"?><root><item>test</item></root>')
    
    test_files['php'] = Path(temp_dir) / 'test.php'
    test_files['php'].write_text('<?php function hello() { echo "Hello World"; } ?>')
    
    test_files['perl'] = Path(temp_dir) / 'test.pl'
    test_files['perl'].write_text('sub hello { print "Hello World\\n"; }')
    
    test_files['markdown'] = Path(temp_dir) / 'test.md'
    test_files['markdown'].write_text('# Hello World\n\nThis is a **test** document.')
    
    test_files['html'] = Path(temp_dir) / 'test.html'
    test_files['html'].write_text('<html><body><h1>Hello World</h1></body></html>')
    
    test_files['fortran'] = Path(temp_dir) / 'test.f90'
    test_files['fortran'].write_text('program hello\n  print *, "Hello World"\nend program')
    
    test_files['vhdl'] = Path(temp_dir) / 'test.vhd'
    test_files['vhdl'].write_text('entity test is end entity; architecture rtl of test is begin end rtl;')
    
    return test_files, temp_dir

def test_all_language_detection():
    """Test that the processor correctly identifies all 27 languages"""
    print("🧪 Testing language detection for all 27 languages...")
    
    test_files, temp_dir = create_comprehensive_test_files()
    processor = MultiLanguageCodeProcessor(temp_dir)
    
    expected_languages = [
        'c', 'cpp', 'python', 'csharp', 'javascript', 'typescript', 'rust', 'java', 'go', 'sql',
        'tcl', 'verilog', 'bash', 'commonlisp', 'elisp', 'scheme', 'lua', 'make', 'json', 'yaml',
        'xml', 'php', 'perl', 'markdown', 'html', 'fortran', 'vhdl'
    ]
    
    detected_count = 0
    parser_count = 0
    
    for lang in expected_languages:
        if lang in test_files:
            filepath = test_files[lang]
            parser, language, lang_name = processor.get_parser_for_file(filepath)
            
            if lang_name == lang:
                detected_count += 1
                print(f"  ✅ {lang.upper()}: {filepath.suffix} -> {lang_name} (parser: {'✓' if parser else '✗'})")
                if parser:
                    parser_count += 1
            else:
                print(f"  ❌ {lang.upper()}: Expected {lang}, got {lang_name}")
    
    print(f"\n📊 Results: {detected_count}/{len(expected_languages)} languages detected correctly")
    print(f"📊 Parsers: {parser_count}/{len(expected_languages)} languages have tree-sitter parsers")
    
    # Cleanup
    import shutil
    shutil.rmtree(temp_dir)
    
    return detected_count == len(expected_languages)

def test_semantic_patterns_coverage():
    """Test that semantic patterns are available for languages"""
    print("\n🔍 Testing semantic patterns coverage...")
    
    processor = MultiLanguageCodeProcessor(".")
    patterns = processor.semantic_patterns
    
    pattern_languages = list(patterns.keys())
    print(f"📋 Languages with semantic patterns: {len(pattern_languages)}")
    
    for lang in sorted(pattern_languages):
        categories = len(patterns[lang])
        total_keywords = sum(len(keywords) for keywords in patterns[lang].values())
        print(f"  ✅ {lang.upper()}: {categories} categories, {total_keywords} keywords")
    
    return len(pattern_languages) > 15  # Should have patterns for most languages

def test_file_processing_sample():
    """Test that files can be processed without errors"""
    print("\n⚙️  Testing file processing for sample languages...")
    
    test_files, temp_dir = create_comprehensive_test_files()
    processor = MultiLanguageCodeProcessor(temp_dir)
    
    # Test a representative sample
    sample_languages = ['python', 'javascript', 'go', 'bash', 'php', 'html', 'json', 'yaml']
    
    success_count = 0
    for lang in sample_languages:
        if lang in test_files:
            try:
                filepath = test_files[lang]
                content = filepath.read_text()
                parser, language, lang_name = processor.get_parser_for_file(filepath)
                
                if parser:
                    # Try to parse the file
                    tree = parser.parse(bytes(content, 'utf8'))
                    print(f"  ✅ {lang.upper()}: Parsed successfully ({len(content)} chars)")
                else:
                    print(f"  ⚠️  {lang.upper()}: No parser available, using text processing")
                
                success_count += 1
                
            except Exception as e:
                print(f"  ❌ {lang.upper()}: Error - {e}")
    
    # Cleanup
    import shutil
    shutil.rmtree(temp_dir)
    
    return success_count == len(sample_languages)

def main():
    """Run comprehensive language support tests"""
    print("🚀 Testing Comprehensive Language Support (27 Languages)")
    print("=" * 70)
    
    try:
        # Test language detection
        detection_success = test_all_language_detection()
        
        # Test semantic patterns
        patterns_success = test_semantic_patterns_coverage()
        
        # Test file processing
        processing_success = test_file_processing_sample()
        
        print("\n" + "=" * 70)
        
        if detection_success and patterns_success and processing_success:
            print("✅ All comprehensive language tests passed!")
            print("🎉 Code analyzer now supports 27 programming languages!")
        else:
            print("⚠️  Some tests had issues, but basic functionality is working")
            
        print(f"📊 Detection: {'✅' if detection_success else '❌'}")
        print(f"📊 Patterns: {'✅' if patterns_success else '❌'}")
        print(f"📊 Processing: {'✅' if processing_success else '❌'}")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
