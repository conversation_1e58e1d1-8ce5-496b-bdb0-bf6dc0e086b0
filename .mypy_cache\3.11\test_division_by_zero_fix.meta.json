{"data_mtime": 1751299518, "dep_lines": [7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["vector_db_creator", "code_preprocessor", "tempfile", "shutil", "pathlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "typing", "typing_extensions"], "hash": "4d9d23b26672d06696011a6954e4602ab1fb820f", "id": "test_division_by_zero_fix", "ignore_all": false, "interface_hash": "fcf3d21c8674b5ebb457743b6b35934ca11282eb", "mtime": 1751299607, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_division_by_zero_fix.py", "plugin_data": null, "size": 5420, "suppressed": [], "version_id": "1.15.0"}