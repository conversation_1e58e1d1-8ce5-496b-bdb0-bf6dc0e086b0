# code_preprocessor.py
import os
import re
from pathlib import Path
import hashlib
import json
from typing import Dict, List
from datetime import datetime

class MultiLanguageCodeProcessor:
    def __init__(self, repo_path):
        self.repo_path = Path(repo_path)
        
        # Use tree-sitter-language-pack for all languages
        try:
            from tree_sitter_language_pack import get_language, get_parser

            # C/C++ languages
            self.c_language = get_language('c')
            self.c_parser = get_parser('c')
            self.cpp_language = get_language('cpp')
            self.cpp_parser = get_parser('cpp')

            # Python
            self.python_language = get_language('python')
            self.python_parser = get_parser('python')

            # Try different names for C#
            self.csharp_language = None
            self.csharp_parser = None
            for csharp_name in ['c_sharp', 'csharp', 'cs']:
                try:
                    self.csharp_language = get_language(csharp_name)
                    self.csharp_parser = get_parser(csharp_name)
                    break
                except Exception:
                    continue

            # JavaScript/TypeScript
            self.javascript_language = None
            self.javascript_parser = None
            self.typescript_language = None
            self.typescript_parser = None
            try:
                self.javascript_language = get_language('javascript')
                self.javascript_parser = get_parser('javascript')
            except Exception:
                print("Warning: JavaScript tree-sitter support not available")

            try:
                self.typescript_language = get_language('typescript')
                self.typescript_parser = get_parser('typescript')
            except Exception:
                print("Warning: TypeScript tree-sitter support not available")

            # Rust
            self.rust_language = None
            self.rust_parser = None
            try:
                self.rust_language = get_language('rust')
                self.rust_parser = get_parser('rust')
            except Exception:
                print("Warning: Rust tree-sitter support not available")

            # Java
            self.java_language = None
            self.java_parser = None
            try:
                self.java_language = get_language('java')
                self.java_parser = get_parser('java')
            except Exception:
                print("Warning: Java tree-sitter support not available")

            # Go
            self.go_language = None
            self.go_parser = None
            try:
                self.go_language = get_language('go')
                self.go_parser = get_parser('go')
            except Exception:
                print("Warning: Go tree-sitter support not available")

            # SQL
            self.sql_language = None
            self.sql_parser = None
            try:
                self.sql_language = get_language('sql')
                self.sql_parser = get_parser('sql')
            except Exception:
                print("Warning: SQL tree-sitter support not available")

            # Additional languages
            self.additional_parsers = {}
            additional_languages = [
                'tcl', 'verilog', 'bash', 'commonlisp', 'elisp', 'scheme',
                'lua', 'make', 'json', 'yaml', 'xml', 'php', 'perl',
                'markdown', 'html', 'fortran', 'vhdl'
            ]

            for lang in additional_languages:
                try:
                    parser = get_parser(lang)
                    language = get_language(lang)
                    self.additional_parsers[lang] = (parser, language)
                except Exception:
                    print(f"Warning: {lang.upper()} tree-sitter support not available")
                    self.additional_parsers[lang] = (None, None)

            # Print status
            supported_languages = ['C', 'C++', 'Python']
            if self.csharp_language: supported_languages.append('C#')
            if self.javascript_language: supported_languages.append('JavaScript')
            if self.typescript_language: supported_languages.append('TypeScript')
            if self.rust_language: supported_languages.append('Rust')
            if self.java_language: supported_languages.append('Java')
            if self.go_language: supported_languages.append('Go')
            if self.sql_language: supported_languages.append('SQL')

            # Add additional languages that have parsers
            for lang, (parser, _) in self.additional_parsers.items():
                if parser is not None:
                    supported_languages.append(lang.upper())

            print(f"Using tree-sitter-language-pack for: {', '.join(supported_languages)}")

            if self.csharp_language is None:
                print("Warning: C# tree-sitter support not available, will use basic text processing for .cs files")
        except ImportError as e:
            raise RuntimeError(f"tree-sitter-language-pack is required. Please install it: pip install tree-sitter-language-pack. Error: {e}")
        
        # Initialize semantic analyzers
        self.semantic_patterns = self._init_semantic_patterns()
        self.complexity_thresholds = {
            'high_complexity_lines': 50,
            'high_complexity_params': 5,
            'high_complexity_branches': 5
        }
    
    def _init_semantic_patterns(self) -> Dict[str, Dict[str, List[str]]]:
        """Initialize semantic patterns for different languages and domains."""
        return {
            'c': {
                'memory_management': ['malloc', 'free', 'calloc', 'realloc', 'memcpy', 'memset', 'strdup'],
                'io_operations': ['printf', 'scanf', 'fopen', 'fclose', 'fread', 'fwrite', 'read', 'write'],
                'string_operations': ['strlen', 'strcpy', 'strcat', 'strcmp', 'strstr', 'strtok', 'sprintf'],
                'network_operations': ['socket', 'bind', 'listen', 'accept', 'connect', 'send', 'recv'],
                'thread_operations': ['pthread_create', 'pthread_join', 'pthread_mutex_lock', 'pthread_mutex_unlock'],
                'error_handling': ['errno', 'perror', 'strerror', 'assert', 'exit'],
                'system_calls': ['fork', 'exec', 'wait', 'pipe', 'signal', 'kill'],
                'data_structures': ['struct', 'union', 'enum', 'typedef', 'array', 'pointer']
            },
            'cpp': {
                'memory_management': ['new', 'delete', 'unique_ptr', 'shared_ptr', 'weak_ptr', 'make_unique', 'make_shared'],
                'io_operations': ['cout', 'cin', 'cerr', 'ifstream', 'ofstream', 'fstream', 'iostream'],
                'string_operations': ['string', 'substr', 'find', 'replace', 'append', 'c_str', 'stringstream'],
                'containers': ['vector', 'list', 'map', 'unordered_map', 'set', 'unordered_set', 'queue', 'stack'],
                'algorithms': ['sort', 'find', 'transform', 'for_each', 'accumulate', 'copy', 'remove'],
                'thread_operations': ['thread', 'mutex', 'lock_guard', 'unique_lock', 'condition_variable', 'async'],
                'error_handling': ['try', 'catch', 'throw', 'exception', 'runtime_error', 'logic_error'],
                'oop_concepts': ['class', 'public', 'private', 'protected', 'virtual', 'override', 'polymorphism', 'encapsulation'],
                'templates': ['template', 'typename', 'auto', 'decltype', 'constexpr'],
                'type_qualifiers': ['const', 'volatile', 'mutable']
            },
            'python': {
                'data_structures': ['list', 'dict', 'set', 'tuple', 'collections', 'defaultdict', 'Counter'],
                'io_operations': ['open', 'read', 'write', 'print', 'input', 'json', 'csv', 'pickle'],
                'string_operations': ['str', 'format', 'join', 'split', 'replace', 'strip', 'regex'],
                'error_handling': ['try', 'except', 'finally', 'raise', 'Exception', 'ValueError', 'TypeError'],
                'async_operations': ['async', 'await', 'asyncio', 'coroutine', 'Future', 'Task'],
                'decorators': ['@property', '@staticmethod', '@classmethod', '@decorator', '@functools'],
                'libraries': ['numpy', 'pandas', 'requests', 'matplotlib', 'sklearn', 'tensorflow', 'flask']
            },
            'csharp': {
                'memory_management': ['using', 'dispose', 'finalizer', 'GC', 'IDisposable'],
                'io_operations': ['Console', 'File', 'Stream', 'Reader', 'Writer', 'Path'],
                'string_operations': ['String', 'StringBuilder', 'Regex', 'Format', 'Join', 'Split'],
                'collections': ['List', 'Dictionary', 'HashSet', 'Queue', 'Stack', 'Array', 'IEnumerable'],
                'linq': ['Select', 'Where', 'OrderBy', 'GroupBy', 'Join', 'Aggregate', 'Any', 'All'],
                'async_operations': ['async', 'await', 'Task', 'Task<T>', 'ConfigureAwait', 'CancellationToken'],
                'error_handling': ['try', 'catch', 'finally', 'throw', 'Exception', 'ArgumentException'],
                'oop_concepts': ['class', 'interface', 'abstract', 'virtual', 'override', 'sealed', 'partial'],
                'attributes': ['Attribute', 'Serializable', 'Obsolete', 'DataContract', 'JsonProperty']
            },
            'javascript': {
                'async_operations': ['async', 'await', 'Promise', 'then', 'catch', 'resolve', 'reject'],
                'dom_operations': ['document', 'getElementById', 'querySelector', 'addEventListener', 'createElement'],
                'array_operations': ['map', 'filter', 'reduce', 'forEach', 'find', 'some', 'every', 'sort'],
                'object_operations': ['Object.keys', 'Object.values', 'Object.entries', 'JSON.parse', 'JSON.stringify'],
                'error_handling': ['try', 'catch', 'finally', 'throw', 'Error', 'TypeError', 'ReferenceError'],
                'modules': ['import', 'export', 'require', 'module.exports', 'default'],
                'functions': ['function', 'arrow', '=>', 'callback', 'closure', 'IIFE'],
                'frameworks_and_runtimes': ['React', 'Vue', 'Angular', 'Express', 'Node.js', 'jQuery']
            },
            'typescript': {
                'type_system': ['interface', 'type', 'enum', 'generic', 'union', 'intersection', 'keyof', 'any', 'unknown', 'never'],
                'async_operations': ['async', 'await', 'Promise', 'then', 'catch', 'resolve', 'reject'],
                'decorators': ['@Component', '@Injectable', '@Input', '@Output', '@ViewChild'],
                'array_operations': ['map', 'filter', 'reduce', 'forEach', 'find', 'some', 'every', 'sort'],
                'object_operations': ['Object.keys', 'Object.values', 'Object.entries', 'JSON.parse', 'JSON.stringify'],
                'error_handling': ['try', 'catch', 'finally', 'throw', 'Error', 'TypeError', 'ReferenceError'],
                'modules': ['import', 'export', 'require', 'module.exports', 'default'],
                'frameworks_and_runtimes': ['React', 'Vue', 'Angular', 'Express', 'Node.js', 'NestJS']
            },
            'rust': {
                'memory_management': ['Box', 'Rc', 'Arc', 'RefCell', 'Mutex', 'RwLock', 'unsafe'],
                'error_handling': ['Result', 'Option', 'Ok', 'Err', 'Some', 'None', 'unwrap', 'expect'],
                'traits': ['trait', 'impl', 'derive', 'Clone', 'Copy', 'Debug', 'Display'],
                'collections': ['Vec', 'HashMap', 'HashSet', 'BTreeMap', 'BTreeSet', 'VecDeque'],
                'async_operations': ['async', 'await', 'Future', 'tokio', 'async-std', 'spawn'],
                'pattern_matching': ['match', 'if let', 'while let', 'pattern', 'destructure'],
                'ownership': ['move', 'borrow', 'lifetime', 'reference', 'mutable', 'immutable'],
                'macros': ['macro_rules', 'println!', 'vec!', 'format!', 'panic!', 'assert!']
            },
            'java': {
                'collections': ['ArrayList', 'HashMap', 'HashSet', 'LinkedList', 'TreeMap', 'TreeSet'],
                'io_operations': ['InputStream', 'OutputStream', 'Reader', 'Writer', 'File', 'Scanner'],
                'string_operations': ['String', 'StringBuilder', 'StringBuffer', 'Pattern', 'Matcher'],
                'concurrency': ['Thread', 'Runnable', 'Executor', 'Future', 'CompletableFuture', 'synchronized'],
                'error_handling': ['try', 'catch', 'finally', 'throw', 'throws', 'Exception', 'RuntimeException'],
                'oop_concepts': ['class', 'interface', 'abstract', 'extends', 'implements', 'super', 'this'],
                'annotations': ['@Override', '@Deprecated', '@SuppressWarnings', '@Entity', '@Component'],
                'frameworks': ['Spring', 'Hibernate', 'JUnit', 'Maven', 'Gradle', 'Jackson']
            },
            'go': {
                'concurrency': ['goroutine', 'channel', 'select', 'sync', 'mutex', 'waitgroup', 'context'],
                'error_handling': ['error', 'panic', 'recover', 'defer', 'errors.New', 'fmt.Errorf'],
                'data_structures': ['slice', 'map', 'struct', 'interface', 'array', 'pointer'],
                'io_operations': ['io.Reader', 'io.Writer', 'bufio', 'os.File', 'fmt.Print', 'log'],
                'string_operations': ['strings', 'strconv', 'fmt.Sprintf', 'regexp', 'unicode'],
                'web_frameworks': ['gin', 'echo', 'fiber', 'gorilla', 'net/http', 'chi'],
                'testing': ['testing.T', 'testing.B', 'assert', 'mock', 'testify'],
                'packages': ['package', 'import', 'go mod', 'go get', 'vendor']
            },
            'sql': {
                'ddl_operations': ['CREATE', 'ALTER', 'DROP', 'TRUNCATE', 'COMMENT'],
                'dml_operations': ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'MERGE', 'ON CONFLICT', 'ON DUPLICATE KEY UPDATE'],
                'query_clauses': ['FROM', 'WHERE', 'GROUP BY', 'HAVING', 'ORDER BY', 'LIMIT'],
                'joins': ['INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'FULL JOIN', 'CROSS JOIN'],
                'functions': ['COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'COALESCE', 'CASE'],
                'constraints': ['PRIMARY KEY', 'FOREIGN KEY', 'UNIQUE', 'NOT NULL', 'CHECK'],
                'data_types': ['VARCHAR', 'INTEGER', 'DECIMAL', 'DATE', 'TIMESTAMP', 'BOOLEAN'],
                'advanced': ['CTE', 'WINDOW', 'PARTITION', 'RECURSIVE', 'PIVOT', 'UNPIVOT']
            },
            'tcl': {
                'control_flow': ['if', 'else', 'elseif', 'while', 'for', 'foreach', 'switch'],
                'procedures': ['proc', 'return', 'uplevel', 'upvar', 'namespace'],
                'string_operations': ['string', 'regexp', 'regsub', 'split', 'join', 'format'],
                'list_operations': ['list', 'lappend', 'lindex', 'llength', 'lsort', 'lsearch'],
                'file_operations': ['open', 'close', 'read', 'write', 'puts', 'gets', 'file'],
                'variables': ['set', 'unset', 'global', 'variable', 'array'],
                'evaluation': ['eval', 'expr', 'subst', 'source', 'catch'],
                'packages': ['package', 'require', 'provide', 'load']
            },
            'verilog': {
                'modules': ['module', 'endmodule', 'input', 'output', 'inout', 'wire', 'reg'],
                'data_types': ['integer', 'real', 'time', 'parameter', 'localparam'],
                'behavioral': ['always', 'initial', 'begin', 'end', 'if', 'else', 'case'],
                'assignments': ['assign', 'blocking', 'nonblocking', 'continuous'],
                'timing': ['posedge', 'negedge', 'delay', 'wait', 'fork', 'join'],
                'system_tasks': ['$display', '$monitor', '$time', '$finish', '$stop'],
                'synthesis': ['synthesizable', 'combinational', 'sequential', 'clock'],
                'verification': ['testbench', 'stimulus', 'assertion', 'coverage']
            },
            'bash': {
                'variables': ['export', 'local', 'readonly', 'unset', 'declare'],
                'control_flow': ['if', 'then', 'else', 'elif', 'fi', 'for', 'while', 'case'],
                'functions': ['function', 'return', 'source', 'alias'],
                'file_operations': ['test', 'find', 'grep', 'sed', 'awk', 'sort', 'uniq'],
                'io_operations': ['echo', 'printf', 'read', 'cat', 'tee'],
                'process_control': ['exec', 'eval', 'trap', 'kill', 'jobs', 'bg', 'fg'],
                'string_operations': ['cut', 'tr', 'wc', 'head', 'tail'],
                'system': ['cd', 'pwd', 'ls', 'chmod', 'chown', 'mount']
            },
            'commonlisp': {
                'functions': ['defun', 'lambda', 'funcall', 'apply', 'mapcar', 'reduce'],
                'macros': ['defmacro', 'macro', 'backquote', 'comma', 'splice'],
                'data_structures': ['list', 'cons', 'car', 'cdr', 'append', 'reverse'],
                'control_flow': ['if', 'when', 'unless', 'cond', 'case', 'loop'],
                'variables': ['let', 'let*', 'setf', 'defvar', 'defparameter'],
                'packages': ['defpackage', 'in-package', 'use-package', 'export'],
                'objects': ['defclass', 'defmethod', 'defgeneric', 'make-instance'],
                'io': ['format', 'print', 'read', 'write', 'with-open-file']
            },
            'elisp': {
                'functions': ['defun', 'lambda', 'funcall', 'apply', 'mapcar'],
                'variables': ['defvar', 'defcustom', 'let', 'let*', 'setq'],
                'buffers': ['buffer', 'current-buffer', 'with-current-buffer'],
                'strings': ['concat', 'substring', 'string-match', 'replace-regexp'],
                'lists': ['list', 'cons', 'car', 'cdr', 'append', 'member'],
                'control_flow': ['if', 'when', 'unless', 'cond', 'while', 'dolist'],
                'hooks': ['add-hook', 'remove-hook', 'run-hooks'],
                'keymaps': ['define-key', 'global-set-key', 'local-set-key']
            },
            'scheme': {
                'functions': ['define', 'lambda', 'apply', 'map', 'for-each'],
                'data_structures': ['list', 'cons', 'car', 'cdr', 'append', 'reverse'],
                'control_flow': ['if', 'cond', 'case', 'and', 'or', 'not'],
                'variables': ['let', 'let*', 'letrec', 'set!', 'define'],
                'predicates': ['null?', 'pair?', 'list?', 'number?', 'string?'],
                'io': ['display', 'write', 'read', 'newline'],
                'continuations': ['call/cc', 'call-with-current-continuation'],
                'syntax': ['quote', 'quasiquote', 'unquote', 'syntax-rules']
            },
            'lua': {
                'functions': ['function', 'end', 'return', 'local'],
                'tables': ['table', 'pairs', 'ipairs', 'next', 'rawget', 'rawset'],
                'strings': ['string', 'gsub', 'match', 'find', 'sub', 'format'],
                'control_flow': ['if', 'then', 'else', 'elseif', 'for', 'while', 'repeat'],
                'modules': ['require', 'module', 'package', 'dofile', 'loadfile'],
                'metatables': ['metatable', 'getmetatable', 'setmetatable'],
                'coroutines': ['coroutine', 'yield', 'resume', 'create'],
                'io': ['io', 'print', 'read', 'write', 'open', 'close']
            },
            'make': {
                'targets': ['target', 'phony', 'default', 'all', 'clean', 'install'],
                'variables': ['define', 'endef', 'override', 'export', 'unexport'],
                'functions': ['call', 'eval', 'foreach', 'if', 'shell', 'wildcard'],
                'conditionals': ['ifdef', 'ifndef', 'ifeq', 'ifneq', 'else', 'endif'],
                'includes': ['include', 'sinclude', '-include'],
                'automatic': ['$@', '$<', '$^', '$?', '$*', '$+'],
                'directives': ['.PHONY', '.SUFFIXES', '.DEFAULT', '.PRECIOUS'],
                'patterns': ['%', 'pattern', 'substitution', 'implicit']
            },
            'json': {
                'data_types': ['object', 'array', 'string', 'number', 'boolean', 'null'],
                'structure': ['key', 'value', 'property', 'element'],
                'syntax': ['bracket', 'brace', 'comma', 'colon', 'quote'],
                'validation': ['schema', 'format', 'required', 'type'],
                'parsing': ['parse', 'stringify', 'serialize', 'deserialize'],
                'path': ['jsonpath', 'pointer', 'reference'],
                'manipulation': ['merge', 'patch', 'diff', 'transform'],
                'standards': ['rfc7159', 'ecma-404', 'iso-21778']
            },
            'yaml': {
                'structure': ['document', 'mapping', 'sequence', 'scalar'],
                'syntax': ['indent', 'dash', 'colon', 'pipe', 'fold'],
                'data_types': ['string', 'integer', 'float', 'boolean', 'null'],
                'anchors': ['anchor', 'alias', 'reference', 'merge'],
                'directives': ['%YAML', '%TAG', 'version'],
                'multiline': ['literal', 'folded', 'strip', 'keep', 'clip'],
                'tags': ['tag', 'type', 'explicit', 'implicit'],
                'validation': ['schema', 'constraint', 'pattern']
            },
            'xml': {
                'structure': ['element', 'attribute', 'text', 'comment', 'cdata'],
                'declaration': ['xml', 'version', 'encoding', 'standalone'],
                'namespace': ['xmlns', 'prefix', 'uri', 'default'],
                'dtd': ['doctype', 'entity', 'notation', 'element', 'attlist'],
                'schema': ['xsd', 'complexType', 'simpleType', 'restriction'],
                'xpath': ['path', 'axis', 'predicate', 'function'],
                'processing': ['instruction', 'target', 'data'],
                'validation': ['wellformed', 'valid', 'schema', 'dtd']
            },
            'php': {
                'variables': ['$var', 'global', 'static', 'const', 'define'],
                'functions': ['function', 'return', 'include', 'require', 'class'],
                'arrays': ['array', 'foreach', 'array_map', 'array_filter', 'count'],
                'strings': ['strlen', 'substr', 'strpos', 'str_replace', 'explode'],
                'oop': ['class', 'extends', 'implements', 'public', 'private', 'protected'],
                'database': ['mysqli', 'pdo', 'mysql', 'query', 'prepare'],
                'web': ['$_GET', '$_POST', '$_SESSION', '$_COOKIE', 'header'],
                'frameworks': ['laravel', 'symfony', 'codeigniter', 'zend']
            },
            'perl': {
                'variables': ['my', 'our', 'local', 'scalar', 'array', 'hash'],
                'references': ['ref', 'deref', 'anonymous', 'reference'],
                'regex': ['match', 'substitute', 'split', 'join', 'pattern'],
                'functions': ['sub', 'return', 'shift', 'unshift', 'push', 'pop'],
                'modules': ['use', 'require', 'package', 'import', 'export'],
                'file_io': ['open', 'close', 'read', 'write', 'print', 'chomp'],
                'control': ['if', 'unless', 'while', 'until', 'for', 'foreach'],
                'special': ['$_', '@_', '%ENV', '$!', '$?', '$$']
            },
            'markdown': {
                'headers': ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'atx', 'setext'],
                'emphasis': ['bold', 'italic', 'strikethrough', 'underline'],
                'lists': ['ordered', 'unordered', 'bullet', 'numbered', 'nested'],
                'links': ['inline', 'reference', 'autolink', 'url', 'title'],
                'images': ['alt', 'src', 'title', 'reference', 'inline'],
                'code': ['inline', 'block', 'fenced', 'indented', 'syntax'],
                'tables': ['pipe', 'alignment', 'header', 'row', 'cell'],
                'extensions': ['footnote', 'definition', 'abbreviation', 'math']
            },
            'html': {
                'structure': ['html', 'head', 'body', 'title', 'meta', 'link'],
                'content': ['div', 'span', 'p', 'h1', 'h2', 'h3', 'section'],
                'forms': ['form', 'input', 'button', 'select', 'textarea', 'label'],
                'media': ['img', 'video', 'audio', 'source', 'track'],
                'tables': ['table', 'tr', 'td', 'th', 'thead', 'tbody', 'tfoot'],
                'lists': ['ul', 'ol', 'li', 'dl', 'dt', 'dd'],
                'attributes': ['id', 'class', 'style', 'data', 'aria'],
                'semantic': ['header', 'nav', 'main', 'article', 'aside', 'footer']
            },
            'fortran': {
                'program_units': ['program', 'subroutine', 'function', 'module'],
                'data_types': ['integer', 'real', 'complex', 'logical', 'character'],
                'arrays': ['dimension', 'allocatable', 'pointer', 'target'],
                'control': ['if', 'then', 'else', 'do', 'while', 'select', 'case'],
                'procedures': ['call', 'return', 'contains', 'interface'],
                'modules': ['use', 'only', 'public', 'private', 'protected'],
                'io': ['read', 'write', 'print', 'open', 'close', 'format'],
                'intrinsic': ['abs', 'sqrt', 'sin', 'cos', 'exp', 'log']
            },
            'vhdl': {
                'design_units': ['entity', 'architecture', 'package', 'configuration'],
                'data_types': ['std_logic', 'std_logic_vector', 'integer', 'boolean'],
                'signals': ['signal', 'variable', 'constant', 'port'],
                'processes': ['process', 'wait', 'sensitivity', 'clk', 'reset'],
                'concurrent': ['when', 'else', 'with', 'select', 'generate'],
                'sequential': ['if', 'then', 'elsif', 'case', 'when', 'loop'],
                'attributes': ['event', 'stable', 'rising_edge', 'falling_edge'],
                'libraries': ['ieee', 'std_logic_1164', 'numeric_std', 'textio']
            }
        }

    def get_parser_for_file(self, filepath):
        """Get appropriate parser based on file extension"""
        suffix = filepath.suffix.lower()

        # C++ files
        if suffix in ['.cpp', '.cxx', '.cc', '.c++']:
            return self.cpp_parser, self.cpp_language, 'cpp'

        # C files
        elif suffix in ['.c', '.h']:
            return self.c_parser, self.c_language, 'c'

        # C++ headers
        elif suffix in ['.hpp', '.hxx', '.hh']:
            return self.cpp_parser, self.cpp_language, 'cpp'

        # Python files
        elif suffix in ['.py', '.pyw']:
            return self.python_parser, self.python_language, 'python'

        # C# files
        elif suffix in ['.cs']:
            if self.csharp_parser is not None:
                return self.csharp_parser, self.csharp_language, 'csharp'
            else:
                # Fallback to basic text processing for C# if tree-sitter not available
                return None, None, 'csharp'

        # JavaScript files
        elif suffix in ['.js', '.jsx', '.mjs', '.cjs']:
            if self.javascript_parser is not None:
                return self.javascript_parser, self.javascript_language, 'javascript'
            else:
                return None, None, 'javascript'

        # TypeScript files
        elif suffix in ['.ts', '.tsx']:
            if self.typescript_parser is not None:
                return self.typescript_parser, self.typescript_language, 'typescript'
            else:
                return None, None, 'typescript'

        # Rust files
        elif suffix in ['.rs']:
            if self.rust_parser is not None:
                return self.rust_parser, self.rust_language, 'rust'
            else:
                return None, None, 'rust'

        # Java files
        elif suffix in ['.java']:
            if self.java_parser is not None:
                return self.java_parser, self.java_language, 'java'
            else:
                return None, None, 'java'

        # Go files
        elif suffix in ['.go']:
            if self.go_parser is not None:
                return self.go_parser, self.go_language, 'go'
            else:
                return None, None, 'go'

        # SQL files
        elif suffix in ['.sql', '.ddl', '.dml', '.plsql', '.psql']:
            if self.sql_parser is not None:
                return self.sql_parser, self.sql_language, 'sql'
            else:
                return None, None, 'sql'

        # Additional languages
        elif suffix in ['.tcl']:
            parser, language = self.additional_parsers.get('tcl', (None, None))
            return parser, language, 'tcl'
        elif suffix in ['.v', '.vh', '.sv']:
            parser, language = self.additional_parsers.get('verilog', (None, None))
            return parser, language, 'verilog'
        elif suffix in ['.sh', '.bash', '.zsh']:
            parser, language = self.additional_parsers.get('bash', (None, None))
            return parser, language, 'bash'
        elif suffix in ['.lisp', '.cl']:
            parser, language = self.additional_parsers.get('commonlisp', (None, None))
            return parser, language, 'commonlisp'
        elif suffix in ['.el']:
            parser, language = self.additional_parsers.get('elisp', (None, None))
            return parser, language, 'elisp'
        elif suffix in ['.scm', '.ss']:
            parser, language = self.additional_parsers.get('scheme', (None, None))
            return parser, language, 'scheme'
        elif suffix in ['.lua']:
            parser, language = self.additional_parsers.get('lua', (None, None))
            return parser, language, 'lua'
        elif suffix in ['.mk', '.make'] or filepath.name.lower() in ['makefile', 'gnumakefile']:
            parser, language = self.additional_parsers.get('make', (None, None))
            return parser, language, 'make'
        elif suffix in ['.json']:
            parser, language = self.additional_parsers.get('json', (None, None))
            return parser, language, 'json'
        elif suffix in ['.yaml', '.yml']:
            parser, language = self.additional_parsers.get('yaml', (None, None))
            return parser, language, 'yaml'
        elif suffix in ['.xml', '.xsd', '.xsl', '.xslt']:
            parser, language = self.additional_parsers.get('xml', (None, None))
            return parser, language, 'xml'
        elif suffix in ['.php', '.phtml']:
            parser, language = self.additional_parsers.get('php', (None, None))
            return parser, language, 'php'
        elif suffix in ['.pl', '.pm', '.perl']:
            parser, language = self.additional_parsers.get('perl', (None, None))
            return parser, language, 'perl'
        elif suffix in ['.md', '.markdown']:
            parser, language = self.additional_parsers.get('markdown', (None, None))
            return parser, language, 'markdown'
        elif suffix in ['.html', '.htm', '.xhtml']:
            parser, language = self.additional_parsers.get('html', (None, None))
            return parser, language, 'html'
        elif suffix in ['.f', '.f90', '.f95', '.f03', '.f08', '.for', '.ftn']:
            parser, language = self.additional_parsers.get('fortran', (None, None))
            return parser, language, 'fortran'
        elif suffix in ['.vhd', '.vhdl']:
            parser, language = self.additional_parsers.get('vhdl', (None, None))
            return parser, language, 'vhdl'

        else:
            # Default to C parser for unknown extensions
            return self.c_parser, self.c_language, 'c'
    
    def extract_enhanced_metadata(self, node, source_code: str, filepath: Path, language: str) -> Dict:
        """Extract comprehensive metadata including semantic analysis."""
        metadata = {
            # Basic metadata (existing)
            'filepath': str(filepath),
            'relative_path': str(filepath.relative_to(self.repo_path)),
            'language': language,
            'start_line': node.start_point[0] + 1,
            'end_line': node.end_point[0] + 1,
            'processed_date': datetime.now().isoformat(),
            
            # Enhanced metadata (new)
            'complexity_metrics': self._analyze_complexity(node, source_code, language),
            'semantic_tags': self._generate_semantic_tags(node, source_code, language),
            'dependencies': self._extract_dependencies(node, source_code, language),
            'code_patterns': self._identify_code_patterns(node, source_code, language),
            'quality_indicators': self._analyze_code_quality(node, source_code, language),
            'documentation_info': self._extract_documentation(node, source_code, language),
            'api_surface': self._analyze_api_surface(node, source_code, language)
        }
        
        return metadata
    
    def _analyze_complexity(self, node, source_code: str, language: str) -> Dict:
        """Analyze code complexity metrics."""
        content = source_code[node.start_byte:node.end_byte]
        lines = content.split('\n')
        
        non_empty_lines = len([line for line in lines if line.strip()])
        comment_lines = self._count_comment_lines(lines, language)

        complexity = {
            'line_count': len(lines),
            'non_empty_lines': non_empty_lines,
            'comment_lines': comment_lines,
            'code_lines': non_empty_lines - comment_lines,
            'cyclomatic_complexity': self._calculate_cyclomatic_complexity(node, source_code, language),
            'nesting_depth': self._calculate_nesting_depth(node),
            'parameter_count': self._count_parameters(node, language),
            'has_loops': self._has_loops(node),
            'has_conditionals': self._has_conditionals(node),
            'has_exception_handling': self._has_exception_handling(node, language),
            'complexity_score': 'low'  # Will be calculated
        }
        complexity['complexity_score'] = self._calculate_complexity_score(complexity)
        
        return complexity
    
    def _generate_semantic_tags(self, node, source_code: str, language: str) -> List[str]:
        """Generate semantic tags based on code content analysis."""
        content = source_code[node.start_byte:node.end_byte].lower()
        tags = set()
        
        # Get language-specific patterns
        patterns = self.semantic_patterns.get(language, {})
        
        # Check for semantic patterns
        for category, keywords in patterns.items():
            if any(keyword.lower() in content for keyword in keywords):
                tags.add(category)
        
        # Add architectural patterns
        if language in ['cpp', 'csharp']:
            if any(pattern in content for pattern in ['singleton', 'factory', 'observer', 'decorator']):
                tags.add('design_pattern')
        
        # Add performance-related tags
        if any(keyword in content for keyword in ['optimize', 'performance', 'cache', 'benchmark']):
            tags.add('performance_critical')
        
        # Add security-related tags
        if any(keyword in content for keyword in ['auth', 'encrypt', 'decrypt', 'hash', 'security', 'validate']):
            tags.add('security_related')
        
        # Add testing-related tags
        if any(keyword in content for keyword in ['test', 'mock', 'assert', 'verify', 'expect']):
            tags.add('testing')
        
        return list(tags)
    
    def _extract_dependencies(self, node, source_code: str, language: str) -> Dict:
        """Extract function calls, imports, and dependencies."""
        dependencies: Dict[str, List[str]] = {
            'function_calls': [],
            'imports': [],
            'includes': [],
            'external_libraries': [],
            'internal_calls': []
        }
        
        if language == 'python':
            dependencies = self._extract_python_dependencies(node, source_code)
        elif language in ['c', 'cpp']:
            dependencies = self._extract_c_cpp_dependencies(node, source_code)
        elif language == 'csharp':
            dependencies = self._extract_csharp_dependencies(node, source_code)
        
        return dependencies
    
    def _identify_code_patterns(self, node, source_code: str, language: str) -> List[str]:
        """Identify common code patterns and idioms."""
        content = source_code[node.start_byte:node.end_byte]
        patterns = []
        
        # Generic patterns
        if 'for' in content or 'while' in content:
            patterns.append('iterative')
        
        if 'if' in content and 'else' in content:
            patterns.append('conditional_logic')
        
        # Language-specific patterns
        if language == 'python':
            if 'with ' in content:
                patterns.append('context_manager')
            if 'yield' in content:
                patterns.append('generator')
            if '[' in content and 'for' in content and ']' in content:
                patterns.append('list_comprehension')
        
        elif language == 'cpp':
            if 'RAII' in content or ('constructor' in content and 'destructor' in content):
                patterns.append('raii')
            if 'template' in content:
                patterns.append('template_metaprogramming')
            if 'std::' in content:
                patterns.append('standard_library')
        
        elif language == 'csharp':
            if 'using' in content and '(' in content:
                patterns.append('using_statement')
            if 'async' in content and 'await' in content:
                patterns.append('async_await')
            if 'LINQ' in content or '.Select(' in content:
                patterns.append('linq')
        
        return patterns
    
    def _analyze_code_quality(self, node, source_code: str, language: str) -> Dict:
        """Analyze code quality indicators."""
        content = source_code[node.start_byte:node.end_byte]
        lines = content.split('\n')
        
        quality = {
            'has_documentation': self._has_documentation(content, language),
            'has_error_handling': self._has_exception_handling(node, language),
            'has_logging': any(keyword in content.lower() for keyword in ['log', 'debug', 'info', 'error', 'warn']),
            'has_constants': self._has_constants(content, language),
            'has_magic_numbers': self._has_magic_numbers(content),
            'naming_convention': self._check_naming_convention(content, language),
            'code_smell_indicators': self._detect_code_smells(content, language),
            'maintainability_score': 'good'  # Will be calculated
        }
        
        quality['maintainability_score'] = self._calculate_maintainability_score(quality)
        
        return quality
    
    def _extract_documentation(self, node, source_code: str, language: str) -> Dict:
        """Extract documentation and comments."""
        content = source_code[node.start_byte:node.end_byte]
        
        doc_info = {
            'has_docstring': False,
            'has_comments': False,
            'comment_ratio': 0.0,
            'doc_type': '',
            'doc_length': 0
        }
        
        lines = content.split('\n')
        comment_lines = self._count_comment_lines(lines, language)
        total_lines = len([line for line in lines if line.strip()])
        
        doc_info['has_comments'] = comment_lines > 0
        doc_info['comment_ratio'] = comment_lines / max(total_lines, 1)
        
        # Language-specific documentation detection
        if language == 'python':
            if '"""' in content or "'''" in content:
                doc_info['has_docstring'] = True
                doc_info['doc_type'] = 'docstring'
        
        elif language in ['c', 'cpp']:
            if '/**' in content or '/*!' in content:
                doc_info['has_docstring'] = True
                doc_info['doc_type'] = 'doxygen'
        
        elif language == 'csharp':
            if '///' in content or '/**' in content:
                doc_info['has_docstring'] = True
                doc_info['doc_type'] = 'xml_doc'
        
        return doc_info
    
    def _analyze_api_surface(self, node, source_code: str, language: str) -> Dict:
        """Analyze public API surface and visibility."""
        content = source_code[node.start_byte:node.end_byte]
        
        api_info = {
            'visibility': 'unknown',
            'is_public': False,
            'is_static': False,
            'is_abstract': False,
            'is_virtual': False,
            'return_type': None,
            'parameters': []
        }
        
        # Language-specific API analysis
        if language == 'python':
            if not content.strip().startswith('_'):
                api_info['is_public'] = True
                api_info['visibility'] = 'public'
            elif content.strip().startswith('__'):
                api_info['visibility'] = 'private'
            else:
                api_info['visibility'] = 'protected'
        
        elif language in ['c', 'cpp']:
            if 'static' in content:
                api_info['is_static'] = True
            if 'virtual' in content:
                api_info['is_virtual'] = True
            if 'public:' in content:
                api_info['visibility'] = 'public'
                api_info['is_public'] = True
            elif 'private:' in content:
                api_info['visibility'] = 'private'
            elif 'protected:' in content:
                api_info['visibility'] = 'protected'
        
        elif language == 'csharp':
            if 'public' in content:
                api_info['visibility'] = 'public'
                api_info['is_public'] = True
            elif 'private' in content:
                api_info['visibility'] = 'private'
            elif 'protected' in content:
                api_info['visibility'] = 'protected'
            elif 'internal' in content:
                api_info['visibility'] = 'internal'
            
            if 'static' in content:
                api_info['is_static'] = True
            if 'abstract' in content:
                api_info['is_abstract'] = True
            if 'virtual' in content:
                api_info['is_virtual'] = True
        
        return api_info
    
    # Helper methods for complexity analysis
    
    def _count_comment_lines(self, lines: List[str], language: str) -> int:
        """Count comment lines based on language."""
        comment_count = 0
        in_block_comment = False
        
        for line in lines:
            stripped = line.strip()
            
            if language == 'python':
                if stripped.startswith('#'):
                    comment_count += 1
                elif stripped.startswith('"""') or stripped.startswith("'''"):
                    comment_count += 1
            
            elif language in ['c', 'cpp', 'csharp']:
                if stripped.startswith('//'):
                    comment_count += 1
                elif '/*' in stripped:
                    comment_count += 1
                    in_block_comment = True
                elif '*/' in stripped and in_block_comment:
                    comment_count += 1
                    in_block_comment = False
                elif in_block_comment:
                    comment_count += 1
        
        return comment_count
    
    def _calculate_cyclomatic_complexity(self, node, source_code: str, language: str) -> int:
        """Calculate cyclomatic complexity by counting decision points."""
        content = source_code[node.start_byte:node.end_byte].lower()
        
        # Count decision points
        decision_keywords = ['if', 'else', 'elif', 'while', 'for', 'switch', 'case', 'catch', '&&', '||', '?']
        
        complexity = 1  # Base complexity
        for keyword in decision_keywords:
            complexity += content.count(keyword)
        
        return min(complexity, 20)  # Cap at 20 for sanity
    
    def _calculate_nesting_depth(self, node) -> int:
        """Calculate maximum nesting depth in the code."""
        def traverse_depth(node, current_depth=0):
            max_depth = current_depth
            
            # Nodes that increase nesting depth
            nesting_nodes = ['if_statement', 'while_statement', 'for_statement', 'try_statement', 'function_definition', 'class_definition']
            
            if node.type in nesting_nodes:
                current_depth += 1
            
            for child in node.children:
                child_depth = traverse_depth(child, current_depth)
                max_depth = max(max_depth, child_depth)
            
            return max_depth
        
        return traverse_depth(node)
    
    def _count_parameters(self, node, language: str) -> int:
        """Count parameters in function definitions."""
        def count_params(node):
            if language == 'python':
                if node.type == 'parameters':
                    return len([child for child in node.children if child.type == 'identifier'])
            elif language in ['c', 'cpp']:
                if node.type == 'parameter_list':
                    return len([child for child in node.children if child.type == 'parameter_declaration'])
            elif language == 'csharp':
                if node.type == 'parameter_list':
                    return len([child for child in node.children if child.type == 'parameter'])
            
            max_params = 0
            for child in node.children:
                max_params = max(max_params, count_params(child))
            return max_params
        
        return count_params(node)
    
    def _has_loops(self, node) -> bool:
        """Check if code contains loops."""
        def has_loop_node(node):
            loop_types = ['for_statement', 'while_statement', 'do_statement', 'for_in_statement']
            if node.type in loop_types:
                return True
            return any(has_loop_node(child) for child in node.children)
        
        return has_loop_node(node)
    
    def _has_conditionals(self, node) -> bool:
        """Check if code contains conditional statements."""
        def has_conditional_node(node):
            conditional_types = ['if_statement', 'conditional_expression', 'switch_statement']
            if node.type in conditional_types:
                return True
            return any(has_conditional_node(child) for child in node.children)
        
        return has_conditional_node(node)
    
    def _has_exception_handling(self, node, language: str) -> bool:
        """Check if code contains exception handling."""
        def has_exception_node(node):
            if language == 'python':
                exception_types = ['try_statement', 'except_clause', 'raise_statement']
            elif language in ['c', 'cpp']:
                exception_types = ['try_statement', 'catch_clause', 'throw_statement']
            elif language == 'csharp':
                exception_types = ['try_statement', 'catch_clause', 'throw_statement', 'finally_clause']
            else:
                return False
            
            if node.type in exception_types:
                return True
            return any(has_exception_node(child) for child in node.children)
        
        return has_exception_node(node)
    
    def _calculate_complexity_score(self, complexity: Dict) -> str:
        """Calculate overall complexity score."""
        score = 0
        
        # Line count impact
        if complexity['line_count'] > self.complexity_thresholds['high_complexity_lines']:
            score += 2
        elif complexity['line_count'] > 20:
            score += 1
        
        # Cyclomatic complexity impact
        if complexity['cyclomatic_complexity'] > self.complexity_thresholds['high_complexity_branches']:
            score += 2
        elif complexity['cyclomatic_complexity'] > 3:
            score += 1
        
        # Parameter count impact
        if complexity['parameter_count'] > self.complexity_thresholds['high_complexity_params']:
            score += 2
        elif complexity['parameter_count'] > 3:
            score += 1
        
        # Nesting depth impact
        if complexity['nesting_depth'] > 4:
            score += 2
        elif complexity['nesting_depth'] > 2:
            score += 1
        
        # Return score
        if score >= 5:
            return 'very_high'
        elif score >= 3:
            return 'high'
        elif score >= 1:
            return 'medium'
        else:
            return 'low'
    
    # Helper methods for quality analysis
    
    def _has_documentation(self, content: str, language: str) -> bool:
        """Check if code has documentation."""
        if language == 'python':
            return '"""' in content or "'''" in content or content.count('#') > 2
        elif language in ['c', 'cpp']:
            return '/**' in content or content.count('//') > 2
        elif language == 'csharp':
            return '///' in content or '/**' in content or content.count('//') > 2
        return False
    
    def _has_constants(self, content: str, language: str) -> bool:
        """Check if code defines constants."""
        if language == 'python':
            return bool(re.search(r'[A-Z_]{2,}', content))
        elif language in ['c', 'cpp']:
            return 'const' in content or '#define' in content
        elif language == 'csharp':
            return 'const' in content or 'readonly' in content
        return False
    
    def _has_magic_numbers(self, content: str) -> bool:
        """Detect magic numbers (hardcoded numeric values)."""
        # Simple heuristic: numbers that aren't 0, 1, -1, or small powers of 2
        numbers = re.findall(r'\b\d+\b', content)
        magic_numbers = [int(n) for n in numbers if int(n) not in [0, 1, 2, 4, 8, 16, 32, 64, 100, 1000]]
        return len(magic_numbers) > 2
    
    def _check_naming_convention(self, content: str, language: str) -> str:
        """Check naming convention compliance."""
        if language == 'python':
            # Check for snake_case
            if re.search(r'[a-z_][a-z0-9_]*', content):
                return 'snake_case'
        elif language in ['c', 'cpp']:
            # Check for various conventions
            if re.search(r'[a-z][a-zA-Z0-9]*', content):
                return 'camelCase'
            elif re.search(r'[a-z_][a-z0-9_]*', content):
                return 'snake_case'
        elif language == 'csharp':
            # Check for PascalCase
            if re.search(r'[A-Z][a-zA-Z0-9]*', content):
                return 'PascalCase'
        
        return 'mixed'
    
    def _detect_code_smells(self, content: str, language: str) -> List[str]:
        """Detect potential code smells."""
        smells = []
        
        # Long parameter lists
        if content.count(',') > 5:
            smells.append('long_parameter_list')
        
        # Duplicated code patterns
        lines = content.split('\n')
        if len(set(lines)) < len(lines) * 0.8:
            smells.append('duplicated_code')
        
        # Large function
        if len(lines) > 50:
            smells.append('large_function')
        
        # Deep nesting
        max_indent = max(len(line) - len(line.lstrip()) for line in lines if line.strip())
        if max_indent > 16:  # Assuming 4-space indentation
            smells.append('deep_nesting')
        
        return smells
    
    def _calculate_maintainability_score(self, quality: Dict) -> str:
        """Calculate overall maintainability score."""
        score = 0
        
        # Positive factors
        if quality['has_documentation']:
            score += 2
        if quality['has_error_handling']:
            score += 2
        if quality['has_logging']:
            score += 1
        if quality['has_constants']:
            score += 1
        if quality['naming_convention'] in ['snake_case', 'camelCase', 'PascalCase']:
            score += 1
        
        # Negative factors
        if quality['has_magic_numbers']:
            score -= 2
        score -= len(quality['code_smell_indicators'])
        
        # Return score
        if score >= 4:
            return 'excellent'
        elif score >= 2:
            return 'good'
        elif score >= 0:
            return 'fair'
        else:
            return 'poor'
    
    # Language-specific dependency extraction
    
    def _extract_python_dependencies(self, node, source_code: str) -> Dict:
        """Extract Python-specific dependencies."""
        content = source_code[node.start_byte:node.end_byte]
        
        dependencies: Dict[str, List[str]] = {
            'function_calls': [],
            'imports': [],
            'includes': [],
            'external_libraries': [],
            'internal_calls': []
        }
        
        # Extract imports
        import_patterns = [
            r'import\s+([a-zA-Z_][a-zA-Z0-9_.]*)',
            r'from\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s+import'
        ]
        
        for pattern in import_patterns:
            matches = re.findall(pattern, content)
            dependencies['imports'].extend(matches)
        
        # Extract function calls
        function_calls = re.findall(r'([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', content)
        dependencies['function_calls'] = list(set(function_calls))
        
        # Identify external libraries
        common_libraries = ['numpy', 'pandas', 'requests', 'matplotlib', 'sklearn', 'tensorflow', 'flask', 'django']
        for lib in common_libraries:
            if lib in content:
                dependencies['external_libraries'].append(lib)
        
        return dependencies
    
    def _extract_c_cpp_dependencies(self, node, source_code: str) -> Dict:
        """Extract C/C++-specific dependencies."""
        content = source_code[node.start_byte:node.end_byte]
        
        dependencies: Dict[str, List[str]] = {
            'function_calls': [],
            'imports': [],
            'includes': [],
            'external_libraries': [],
            'internal_calls': []
        }
        
        # Extract includes
        include_patterns = [
            r'#include\s*[<"]([^>"]+)[>"]',
            r'#include\s+([a-zA-Z_][a-zA-Z0-9_.]*)'
        ]
        
        for pattern in include_patterns:
            matches = re.findall(pattern, content)
            dependencies['includes'].extend(matches)
        
        # Extract function calls
        function_calls = re.findall(r'([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', content)
        dependencies['function_calls'] = list(set(function_calls))
        
        # Identify standard library usage
        std_headers = ['stdio.h', 'stdlib.h', 'string.h', 'math.h', 'iostream', 'vector', 'string', 'algorithm']
        for header in std_headers:
            if header in content:
                dependencies['external_libraries'].append(header)
        
        # Identify system calls
        system_calls = ['malloc', 'free', 'printf', 'scanf', 'socket', 'bind', 'listen', 'accept']
        for call in system_calls:
            if call in content:
                dependencies['internal_calls'].append(call)
        
        return dependencies
    
    def _extract_csharp_dependencies(self, node, source_code: str) -> Dict:
        """Extract C#-specific dependencies."""
        content = source_code[node.start_byte:node.end_byte]
        
        dependencies: Dict[str, List[str]] = {
            'function_calls': [],
            'imports': [],
            'includes': [],
            'external_libraries': [],
            'internal_calls': []
        }
        
        # Extract using statements
        using_patterns = [
            r'using\s+([a-zA-Z_][a-zA-Z0-9_.]*);',
            r'using\s+static\s+([a-zA-Z_][a-zA-Z0-9_.]*);'
        ]
        
        for pattern in using_patterns:
            matches = re.findall(pattern, content)
            dependencies['imports'].extend(matches)
        
        # Extract method calls
        method_calls = re.findall(r'([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', content)
        dependencies['function_calls'] = list(set(method_calls))
        
        # Identify .NET framework usage
        dotnet_namespaces = ['System', 'System.Collections', 'System.IO', 'System.Net', 'System.Threading', 'Microsoft']
        for namespace in dotnet_namespaces:
            if namespace in content:
                dependencies['external_libraries'].append(namespace)
        
        return dependencies
    
    # Updated extraction methods with enhanced metadata
    
    def extract_functions(self, source_code, filepath):
        """Extract function definitions with enhanced metadata"""
        parser, language, lang_name = self.get_parser_for_file(filepath)

        if parser is None:
            return []

        tree = parser.parse(bytes(source_code, "utf8"))
        functions = []
        
        def traverse(node, depth=0):
            # Handle different function types based on language
            if lang_name == 'python':
                if node.type == 'function_definition':
                    func_text = source_code[node.start_byte:node.end_byte]
                    func_name = self._get_python_function_name(node, source_code)
                    
                    # Extract enhanced metadata
                    enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                    enhanced_metadata.update({
                        'type': 'function',
                        'function_name': func_name,
                        'chunk_id': hashlib.md5(f"{filepath}_{func_name}".encode()).hexdigest()[:8]
                    })
                    
                    functions.append({
                        'name': func_name,
                        'content': func_text,
                        'start_line': node.start_point[0] + 1,
                        'end_line': node.end_point[0] + 1,
                        'filepath': str(filepath),
                        'metadata': enhanced_metadata
                    })
            
            elif lang_name == 'csharp':
                if node.type == 'method_declaration':
                    func_text = source_code[node.start_byte:node.end_byte]
                    func_name = self._get_csharp_method_name(node, source_code)
                    
                    enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                    enhanced_metadata.update({
                        'type': 'method',
                        'method_name': func_name,
                        'chunk_id': hashlib.md5(f"{filepath}_{func_name}".encode()).hexdigest()[:8]
                    })
                    
                    functions.append({
                        'name': func_name,
                        'content': func_text,
                        'start_line': node.start_point[0] + 1,
                        'end_line': node.end_point[0] + 1,
                        'filepath': str(filepath),
                        'metadata': enhanced_metadata
                    })
            
            else:  # C/C++
                if node.type == 'function_definition':
                    func_text = source_code[node.start_byte:node.end_byte]
                    func_name = self._get_function_name(node, source_code)
                    
                    enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                    enhanced_metadata.update({
                        'type': 'function',
                        'function_name': func_name,
                        'chunk_id': hashlib.md5(f"{filepath}_{func_name}".encode()).hexdigest()[:8]
                    })
                    
                    functions.append({
                        'name': func_name,
                        'content': func_text,
                        'start_line': node.start_point[0] + 1,
                        'end_line': node.end_point[0] + 1,
                        'filepath': str(filepath),
                        'metadata': enhanced_metadata
                    })
            
            for child in node.children:
                traverse(child, depth + 1)
        
        traverse(tree.root_node)
        return functions
    
    def extract_classes_and_methods(self, source_code, filepath):
        """Extract class definitions and methods with enhanced metadata"""
        parser, language, lang_name = self.get_parser_for_file(filepath)

        if parser is None:
            return []

        tree = parser.parse(bytes(source_code, "utf8"))
        classes = []
        
        def traverse(node):
            class_node_types = []
            
            if lang_name == 'python':
                class_node_types = ['class_definition']
            elif lang_name == 'csharp':
                class_node_types = ['class_declaration', 'interface_declaration', 'struct_declaration']
            else:  # C/C++
                class_node_types = ['class_specifier']
            
            if node.type in class_node_types:
                class_text = source_code[node.start_byte:node.end_byte]
                class_name = self._get_class_name_for_language(node, source_code, lang_name)
                
                # Extract enhanced metadata
                enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                
                # Extract methods within the class
                methods = []
                if lang_name == 'python':
                    methods = self._extract_python_methods_from_class(node, source_code, filepath, lang_name)
                elif lang_name == 'csharp':
                    methods = self._extract_csharp_methods_from_class(node, source_code, filepath, lang_name)
                else:  # C/C++
                    for child in node.children:
                        if child.type == 'field_declaration_list':
                            methods.extend(self._extract_methods_from_body(child, source_code, filepath, lang_name))
                
                enhanced_metadata.update({
                    'type': 'class',
                    'class_name': class_name,
                    'method_count': len(methods),
                    'chunk_id': hashlib.md5(f"{filepath}_{class_name}".encode()).hexdigest()[:8]
                })
                
                classes.append({
                    'name': class_name,
                    'content': class_text,
                    'methods': methods,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath),
                    'metadata': enhanced_metadata
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return classes
    
    def extract_structs_and_typedefs(self, source_code, filepath):
        """Extract struct definitions and typedefs with enhanced metadata"""
        parser, language, lang_name = self.get_parser_for_file(filepath)

        if parser is None:
            return []

        tree = parser.parse(bytes(source_code, "utf8"))
        definitions = []
        
        def traverse(node):
            definition_types = []
            
            if lang_name == 'python':
                definition_types = ['import_statement', 'import_from_statement', 'decorated_definition']
            elif lang_name == 'csharp':
                definition_types = ['struct_declaration', 'enum_declaration', 'delegate_declaration', 'using_directive']
            else:  # C/C++
                definition_types = ['struct_specifier', 'typedef_declaration', 'enum_specifier', 'union_specifier']
            
            if node.type in definition_types:
                def_text = source_code[node.start_byte:node.end_byte]
                
                # Extract enhanced metadata
                enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                enhanced_metadata.update({
                    'type': node.type,
                    'chunk_id': hashlib.md5(f"{filepath}_{node.start_point[0]}".encode()).hexdigest()[:8]
                })
                
                definitions.append({
                    'type': node.type,
                    'content': def_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath),
                    'metadata': enhanced_metadata
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return definitions
    
    def extract_namespaces(self, source_code, filepath):
        """Extract namespace definitions with enhanced metadata"""
        parser, language, lang_name = self.get_parser_for_file(filepath)

        if parser is None:
            return []

        tree = parser.parse(bytes(source_code, "utf8"))
        namespaces = []
        
        # Python doesn't have namespaces, skip for Python files
        if lang_name == 'python':
            return namespaces
        
        def traverse(node):
            namespace_types = []
            
            if lang_name == 'csharp':
                namespace_types = ['namespace_declaration']
            else:  # C/C++
                namespace_types = ['namespace_definition']
            
            if node.type in namespace_types:
                ns_text = source_code[node.start_byte:node.end_byte]
                ns_name = self._get_namespace_name_for_language(node, source_code, lang_name)
                
                # Extract enhanced metadata
                enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                enhanced_metadata.update({
                    'type': 'namespace',
                    'namespace_name': ns_name,
                    'chunk_id': hashlib.md5(f"{filepath}_{ns_name}_ns".encode()).hexdigest()[:8]
                })
                
                namespaces.append({
                    'name': ns_name,
                    'content': ns_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath),
                    'metadata': enhanced_metadata
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return namespaces
    
    def extract_template_definitions(self, source_code, filepath):
        """Extract template definitions with enhanced metadata (C++ only)"""
        parser, language, lang_name = self.get_parser_for_file(filepath)

        if parser is None:
            return []

        tree = parser.parse(bytes(source_code, "utf8"))
        templates = []
        
        # Only applicable to C++
        if lang_name != 'cpp':
            return templates
        
        def traverse(node):
            if node.type == 'template_declaration':
                template_text = source_code[node.start_byte:node.end_byte]
                
                # Extract enhanced metadata
                enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                enhanced_metadata.update({
                    'type': 'template',
                    'chunk_id': hashlib.md5(f"{filepath}_template_{node.start_point[0]}".encode()).hexdigest()[:8]
                })
                
                templates.append({
                    'content': template_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath),
                    'metadata': enhanced_metadata
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return templates
    
    # Updated helper methods for enhanced metadata extraction
    
    def _extract_python_methods_from_class(self, class_node, source_code, filepath, lang_name):
        """Extract method definitions from Python class with enhanced metadata"""
        methods = []
        
        def traverse_class(node):
            if node.type == 'function_definition':
                method_text = source_code[node.start_byte:node.end_byte]
                method_name = self._get_python_function_name(node, source_code)
                
                # Extract enhanced metadata
                enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                enhanced_metadata.update({
                    'type': 'method',
                    'method_name': method_name,
                    'chunk_id': hashlib.md5(f"{filepath}_{method_name}".encode()).hexdigest()[:8]
                })
                
                methods.append({
                    'name': method_name,
                    'content': method_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'metadata': enhanced_metadata
                })
            
            for child in node.children:
                traverse_class(child)
        
        traverse_class(class_node)
        return methods
    
    def _extract_csharp_methods_from_class(self, class_node, source_code, filepath, lang_name):
        """Extract method definitions from C# class with enhanced metadata"""
        methods = []
        
        def traverse_class(node):
            if node.type == 'method_declaration':
                method_text = source_code[node.start_byte:node.end_byte]
                method_name = self._get_csharp_method_name(node, source_code)
                
                # Extract enhanced metadata
                enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                enhanced_metadata.update({
                    'type': 'method',
                    'method_name': method_name,
                    'chunk_id': hashlib.md5(f"{filepath}_{method_name}".encode()).hexdigest()[:8]
                })
                
                methods.append({
                    'name': method_name,
                    'content': method_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'metadata': enhanced_metadata
                })
            
            for child in node.children:
                traverse_class(child)
        
        traverse_class(class_node)
        return methods
    
    def _extract_methods_from_body(self, body_node, source_code, filepath, lang_name):
        """Extract method definitions from C++ class body with enhanced metadata"""
        methods = []
        
        def traverse_body(node):
            if node.type == 'function_definition':
                method_text = source_code[node.start_byte:node.end_byte]
                method_name = self._get_function_name(node, source_code)
                
                # Extract enhanced metadata
                enhanced_metadata = self.extract_enhanced_metadata(node, source_code, filepath, lang_name)
                enhanced_metadata.update({
                    'type': 'method',
                    'method_name': method_name,
                    'chunk_id': hashlib.md5(f"{filepath}_{method_name}".encode()).hexdigest()[:8]
                })
                
                methods.append({
                    'name': method_name,
                    'content': method_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'metadata': enhanced_metadata
                })
            
            for child in node.children:
                traverse_body(child)
        
        traverse_body(body_node)
        return methods
    
    # Language-specific helper methods (unchanged but included for completeness)
    
    def _get_python_function_name(self, func_node, source_code):
        """Extract function name from Python function_definition node"""
        for child in func_node.children:
            if child.type == 'identifier':
                return source_code[child.start_byte:child.end_byte]
        return "unknown"
    
    def _get_csharp_method_name(self, method_node, source_code):
        """Extract method name from C# method_declaration node"""
        for child in method_node.children:
            if child.type == 'identifier':
                return source_code[child.start_byte:child.end_byte]
        return "unknown"
    
    def _get_function_name(self, func_node, source_code):
        """Extract function name from C/C++ function_definition node"""
        for child in func_node.children:
            if child.type == 'function_declarator':
                for grandchild in child.children:
                    if grandchild.type == 'identifier':
                        return source_code[grandchild.start_byte:grandchild.end_byte]
        return "unknown"
    
    def _get_class_name_for_language(self, class_node, source_code, lang_name):
        """Extract class name for different languages"""
        if lang_name == 'python':
            for child in class_node.children:
                if child.type == 'identifier':
                    return source_code[child.start_byte:child.end_byte]
        
        elif lang_name == 'csharp':
            for child in class_node.children:
                if child.type == 'identifier':
                    return source_code[child.start_byte:child.end_byte]
        
        else:  # C/C++
            for child in class_node.children:
                if child.type == 'type_identifier':
                    return source_code[child.start_byte:child.end_byte]
        
        return "unknown"
    
    def _get_namespace_name_for_language(self, ns_node, source_code, lang_name):
        """Extract namespace name for different languages"""
        for child in ns_node.children:
            if child.type == 'identifier':
                return source_code[child.start_byte:child.end_byte]
        return "anonymous"
    
    # Updated process_file method with enhanced metadata
    
    def process_file(self, filepath):
        """Process a single source file with enhanced metadata extraction"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading {filepath}: {e}")
            return []
        
        # Determine language
        parser, language, lang_name = self.get_parser_for_file(filepath)
        
        # Extract components based on language
        functions = self.extract_functions(content, filepath)
        definitions = self.extract_structs_and_typedefs(content, filepath)
        classes = self.extract_classes_and_methods(content, filepath)
        namespaces = self.extract_namespaces(content, filepath)
        
        # Templates only for C++
        templates = []
        if lang_name == 'cpp':
            templates = self.extract_template_definitions(content, filepath)

        # For C# files without tree-sitter, add basic text-based extraction
        if lang_name == 'csharp' and parser is None:
            basic_info = self.extract_csharp_basic_info(content, filepath)
            if basic_info:
                functions.extend(basic_info.get('methods', []))
                classes.extend(basic_info.get('classes', []))
                namespaces.extend(basic_info.get('namespaces', []))

        # Create chunks with enhanced metadata
        chunks = []
        
        # Add file header chunk with enhanced metadata
        header_lines = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # Language-specific header detection
            if lang_name == 'python':
                if (stripped.startswith(('import ', 'from ', '#', 'encoding:', 'coding:')) or
                    stripped.startswith('"""') or stripped.startswith("'''")):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped and not stripped.startswith(('class ', 'def ', 'if ', 'for ', 'while ')):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped:
                    break
            
            elif lang_name == 'csharp':
                if (stripped.startswith(('using ', 'namespace ', '[', '//', '/*')) or
                    'assembly:' in stripped):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped and not stripped.startswith(('class ', 'interface ', 'struct ', 'enum ')):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped:
                    break

            elif lang_name in ['javascript', 'typescript']:
                if (stripped.startswith(('import ', 'export ', 'from ', '//', '/*', '/**')) or
                    stripped.startswith(('type ', 'interface ', 'declare ')) or
                    stripped.startswith(('const ', 'let ', 'var ')) and ('=' not in stripped or 'require(' in stripped)):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped and not stripped.startswith(('class ', 'function ', 'const ', 'let ', 'var ', 'if ', 'for ', 'while ')):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped:
                    break

            else:  # C/C++ and other languages
                if stripped.startswith(('#include', '#define', '#ifndef', '#ifdef', '#pragma', 'using namespace')):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped and not stripped.startswith(('//','/*')):
                    break
        
        if header_lines:
            # Create a dummy node for header analysis
            header_content = '\n'.join(header_lines)
            header_metadata = {
                'type': 'header',
                'filepath': str(filepath),
                'relative_path': str(filepath.relative_to(self.repo_path)),
                'language': lang_name,
                'chunk_id': hashlib.md5(f"{filepath}_header".encode()).hexdigest()[:8],
                'processed_date': datetime.now().isoformat(),
                'complexity_metrics': {'line_count': len(header_lines), 'complexity_score': 'low'},
                'semantic_tags': self._generate_semantic_tags_from_content(header_content, lang_name),
                'dependencies': self._extract_dependencies_from_content(header_content, lang_name),
                'code_patterns': ['header_definitions'],
                'quality_indicators': {'has_documentation': True, 'maintainability_score': 'good'},
                'documentation_info': {'has_comments': '//' in header_content or '#' in header_content},
                'api_surface': {'visibility': 'public', 'is_public': True}
            }
            
            chunks.append({
                'content': '\n'.join(header_lines),
                'metadata': header_metadata
            })
        
        # Add function chunks with enhanced metadata
        for func in functions:
            chunks.append({
                'content': f"Function: {func['name']}\nFile: {func['filepath']}\nLines: {func['start_line']}-{func['end_line']}\n\n{func['content']}",
                'metadata': func['metadata']
            })
        
        # Add class chunks with enhanced metadata
        for cls in classes:
            chunks.append({
                'content': f"Class: {cls['name']}\nFile: {cls['filepath']}\nLines: {cls['start_line']}-{cls['end_line']}\n\n{cls['content']}",
                'metadata': cls['metadata']
            })
            
            # Add individual method chunks for large classes
            for method in cls['methods']:
                method_type = 'method' if lang_name in ['cpp', 'csharp'] else 'function'
                method_metadata = method['metadata'].copy()
                method_metadata.update({
                    'class_name': cls['name'],
                    'type': method_type
                })
                
                chunks.append({
                    'content': f"Method: {cls['name']}::{method['name']}\nFile: {cls['filepath']}\nLines: {method['start_line']}-{method['end_line']}\n\n{method['content']}",
                    'metadata': method_metadata
                })
        
        # Add namespace chunks with enhanced metadata
        for ns in namespaces:
            chunks.append({
                'content': f"Namespace: {ns['name']}\nFile: {ns['filepath']}\nLines: {ns['start_line']}-{ns['end_line']}\n\n{ns['content']}",
                'metadata': ns['metadata']
            })
        
        # Add template chunks with enhanced metadata (C++ only)
        for template in templates:
            chunks.append({
                'content': f"Template Definition\nFile: {template['filepath']}\nLines: {template['start_line']}-{template['end_line']}\n\n{template['content']}",
                'metadata': template['metadata']
            })
        
        # Add struct/typedef/enum chunks with enhanced metadata
        for definition in definitions:
            chunks.append({
                'content': f"Definition ({definition['type']})\nFile: {definition['filepath']}\nLines: {definition['start_line']}-{definition['end_line']}\n\n{definition['content']}",
                'metadata': definition['metadata']
            })
        
        return chunks
    
    # Helper methods for content-based analysis (when nodes aren't available)
    
    def _generate_semantic_tags_from_content(self, content: str, language: str) -> List[str]:
        """Generate semantic tags from content string."""
        content_lower = content.lower()
        tags = set()
        
        patterns = self.semantic_patterns.get(language, {})
        for category, keywords in patterns.items():
            if any(keyword.lower() in content_lower for keyword in keywords):
                tags.add(category)
        
        return list(tags)
    
    def _extract_dependencies_from_content(self, content: str, language: str) -> Dict:
        """Extract dependencies from content string."""
        dependencies: Dict[str, List[str]] = {
            'function_calls': [],
            'imports': [],
            'includes': [],
            'external_libraries': [],
            'internal_calls': []
        }
        
        if language == 'python':
            imports = re.findall(r'import\s+([a-zA-Z_][a-zA-Z0-9_.]*)', content)
            from_imports = re.findall(r'from\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s+import', content)
            dependencies['imports'] = imports + from_imports
        
        elif language in ['c', 'cpp']:
            includes = re.findall(r'#include\s*[<"]([^>"]+)[>"]', content)
            dependencies['includes'] = includes
        
        elif language == 'csharp':
            usings = re.findall(r'using\s+([a-zA-Z_][a-zA-Z0-9_.]*);', content)
            dependencies['imports'] = usings
        
        return dependencies
    
    # Original methods for compatibility
    
    def process_repository(self, exclude_dirs=None):
        """Process entire repository recursively with enhanced multi-language support"""
        if exclude_dirs is None:
            exclude_dirs = {'.git', '.svn', 'build', 'dist', '__pycache__', 'node_modules', 
                           'bin', 'obj', '.vs', '.vscode', '.idea', 'packages'}
        
        all_chunks = []
        
        # Extended source extensions to include all 27 supported languages
        source_extensions = {
            # Core languages
            '.c', '.cpp', '.cxx', '.cc', '.c++', '.h', '.hpp', '.hxx', '.hh',  # C/C++
            '.py', '.pyw',  # Python
            '.cs',  # C#
            '.js', '.jsx', '.mjs', '.cjs',  # JavaScript
            '.ts', '.tsx',  # TypeScript
            '.rs',  # Rust
            '.java',  # Java
            '.go',  # Go
            '.sql', '.ddl', '.dml', '.plsql', '.psql',  # SQL
            # Additional languages
            '.tcl',  # TCL
            '.v', '.vh', '.sv',  # Verilog
            '.sh', '.bash', '.zsh',  # Bash
            '.lisp', '.cl',  # CommonLisp
            '.el',  # EmacsLisp
            '.scm', '.ss',  # Scheme
            '.lua',  # Lua
            '.mk', '.make',  # Make (also check for Makefile below)
            '.json',  # JSON
            '.yaml', '.yml',  # YAML
            '.xml', '.xsd', '.xsl', '.xslt',  # XML
            '.php', '.phtml',  # PHP
            '.pl', '.pm', '.perl',  # Perl
            '.md', '.markdown',  # Markdown
            '.html', '.htm', '.xhtml',  # HTML
            '.f', '.f90', '.f95', '.f03', '.f08', '.for', '.ftn',  # Fortran
            '.vhd', '.vhdl'  # VHDL
        }
        source_files = []
        
        def should_exclude_dir(dir_path):
            """Check if directory should be excluded"""
            return any(excluded in str(dir_path).lower() for excluded in exclude_dirs)
        
        print(f"Scanning {self.repo_path} recursively for source files...")
        print("Supported languages: 27 languages including C, C++, Python, C#, JavaScript, TypeScript, Rust, Java, Go, SQL, and 17 more")
        
        for root, dirs, files in os.walk(self.repo_path):
            root_path = Path(root)
            
            # Remove excluded directories from dirs list to prevent walking into them
            dirs[:] = [d for d in dirs if not should_exclude_dir(root_path / d)]
            
            for file in files:
                filepath = root_path / file
                # Check file extension
                if filepath.suffix.lower() in source_extensions:
                    source_files.append(filepath)
                # Special case for Makefile (no extension)
                elif filepath.name.lower() in ['makefile', 'gnumakefile']:
                    source_files.append(filepath)
        
        
        print(f"Found {len(source_files)} source files:")
        
        # Group by extension for summary
        by_ext = {}
        for f in source_files:
            ext = f.suffix.lower()
            by_ext[ext] = by_ext.get(ext, 0) + 1
        
        for ext, count in sorted(by_ext.items()):
            lang_name = self._get_language_name_from_extension(ext)
            print(f"  {ext} ({lang_name}): {count} files")
        
        # Process each file with enhanced metadata
        for filepath in source_files:
            rel_path = filepath.relative_to(self.repo_path)
            print(f"Processing: {rel_path}")
            try:
                chunks = self.process_file(filepath)
                all_chunks.extend(chunks)
                print(f"  -> Generated {len(chunks)} chunks with enhanced metadata")
            except Exception as e:
                print(f"  -> Error processing {rel_path}: {e}")
                continue
        
        print("\nProcessing complete:")
        print(f"  Total files processed: {len(source_files)}")
        print(f"  Total chunks generated: {len(all_chunks)}")
        
        # Show enhanced statistics
        self._show_enhanced_statistics(all_chunks)
        
        return all_chunks
    
    def _show_enhanced_statistics(self, chunks: List[Dict]):
        """Display enhanced statistics about processed chunks."""
        print("\n=== Enhanced Processing Statistics ===")
        
        # Basic counts
        language_chunks: dict[str, int] = {}
        type_chunks: dict[str, int] = {}
        complexity_distribution: dict[str | int, int] = {}
        semantic_tag_frequency: dict[str, int] = {}
        
        for chunk in chunks:
            metadata = chunk['metadata']
            
            # Language distribution
            lang = metadata.get('language', 'unknown')
            language_chunks[lang] = language_chunks.get(lang, 0) + 1
            
            # Type distribution
            chunk_type = metadata.get('type', 'unknown')
            type_chunks[chunk_type] = type_chunks.get(chunk_type, 0) + 1
            
            # Complexity distribution
            complexity = metadata.get('complexity_metrics', {}).get('complexity_score', 'unknown')
            complexity_distribution[complexity] = complexity_distribution.get(complexity, 0) + 1
            
            # Semantic tag frequency
            tags = metadata.get('semantic_tags', [])
            for tag in tags:
                semantic_tag_frequency[tag] = semantic_tag_frequency.get(tag, 0) + 1
        
        print(f"Total chunks: {len(chunks)}")
        
        print("\nLanguage Distribution:")
        if len(chunks) > 0:
            for lang, count in sorted(language_chunks.items()):
                percentage = (count / len(chunks)) * 100
                print(f"  {lang}: {count} chunks ({percentage:.1f}%)")
        else:
            print("  No chunks available for language analysis")
        
        print("\nCode Type Distribution:")
        if len(chunks) > 0:
            for chunk_type, count in sorted(type_chunks.items()):
                percentage = (count / len(chunks)) * 100
                print(f"  {chunk_type}: {count} chunks ({percentage:.1f}%)")
        else:
            print("  No chunks available for type analysis")
        
        print("\nComplexity Distribution:")
        if len(chunks) > 0:
            for complexity, count in sorted(complexity_distribution.items()):
                percentage = (count / len(chunks)) * 100
                print(f"  {complexity}: {count} chunks ({percentage:.1f}%)")
        else:
            print("  No chunks available for complexity analysis")
        
        print("\nTop Semantic Tags:")
        if len(chunks) > 0:
            sorted_tags = sorted(semantic_tag_frequency.items(), key=lambda x: x[1], reverse=True)
            for tag, count in sorted_tags[:10]:  # Show top 10
                percentage = (count / len(chunks)) * 100
                print(f"  {tag}: {count} occurrences ({percentage:.1f}%)")
        else:
            print("  No chunks available for semantic tag analysis")
        
        # Quality metrics
        documented_chunks = sum(1 for chunk in chunks 
                              if chunk['metadata'].get('quality_indicators', {}).get('has_documentation', False))
        error_handling_chunks = sum(1 for chunk in chunks 
                                  if chunk['metadata'].get('quality_indicators', {}).get('has_error_handling', False))
        
        print("\nQuality Metrics:")
        if len(chunks) > 0:
            print(f"  Documented chunks: {documented_chunks}/{len(chunks)} ({(documented_chunks/len(chunks)*100):.1f}%)")
            print(f"  Error handling chunks: {error_handling_chunks}/{len(chunks)} ({(error_handling_chunks/len(chunks)*100):.1f}%)")
        else:
            print("  No chunks available for quality metrics")
        
        # Complexity metrics
        high_complexity_chunks = sum(1 for chunk in chunks 
                                   if chunk['metadata'].get('complexity_metrics', {}).get('complexity_score') in ['high', 'very_high'])
        
        if len(chunks) > 0:
            print(f"  High complexity chunks: {high_complexity_chunks}/{len(chunks)} ({(high_complexity_chunks/len(chunks)*100):.1f}%)")
        else:
            print(f"  High complexity chunks: {high_complexity_chunks}/0 (N/A%)")
        
        print("=" * 50)
    
    def _get_language_name_from_extension(self, ext):
        """Get human-readable language name from file extension"""
        ext_map = {
            '.c': 'C',
            '.h': 'C/C++',
            '.cpp': 'C++',
            '.cxx': 'C++',
            '.cc': 'C++',
            '.c++': 'C++',
            '.hpp': 'C++',
            '.hxx': 'C++',
            '.hh': 'C++',
            '.py': 'Python',
            '.pyw': 'Python',
            '.cs': 'C#'
        }
        return ext_map.get(ext.lower(), 'Unknown')

    def extract_csharp_basic_info(self, content, filepath):
        """Basic C# information extraction using regex when tree-sitter is not available"""
        import re

        info = {
            'classes': [],
            'methods': [],
            'namespaces': []
        }

        lines = content.split('\n')

        # Extract namespaces with enhanced metadata
        namespace_pattern = r'^\s*namespace\s+([A-Za-z_][A-Za-z0-9_.]*)'
        for i, line in enumerate(lines):
            match = re.match(namespace_pattern, line)
            if match:
                # Create basic metadata for regex-extracted items
                basic_metadata = {
                    'type': 'namespace',
                    'namespace_name': match.group(1),
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'language': 'csharp',
                    'start_line': i + 1,
                    'end_line': i + 1,
                    'processed_date': datetime.now().isoformat(),
                    'complexity_metrics': {'line_count': 1, 'complexity_score': 'low'},
                    'semantic_tags': ['namespace'],
                    'dependencies': {'imports': [], 'function_calls': [], 'includes': [], 'external_libraries': [], 'internal_calls': []},
                    'code_patterns': ['namespace_definition'],
                    'quality_indicators': {'has_documentation': False, 'maintainability_score': 'fair'},
                    'documentation_info': {'has_comments': False, 'comment_ratio': 0.0},
                    'api_surface': {'visibility': 'public', 'is_public': True},
                    'chunk_id': hashlib.md5(f"{filepath}_{match.group(1)}_ns".encode()).hexdigest()[:8]
                }
                
                info['namespaces'].append({
                    'name': match.group(1),
                    'content': line,
                    'start_line': i + 1,
                    'end_line': i + 1,
                    'filepath': str(filepath),
                    'metadata': basic_metadata
                })

        # Extract classes with enhanced metadata
        class_pattern = r'^\s*(?:public|private|protected|internal)?\s*(?:static|abstract|sealed)?\s*class\s+([A-Za-z_][A-Za-z0-9_]*)'
        for i, line in enumerate(lines):
            match = re.match(class_pattern, line)
            if match:
                basic_metadata = {
                    'type': 'class',
                    'class_name': match.group(1),
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'language': 'csharp',
                    'start_line': i + 1,
                    'end_line': i + 1,
                    'processed_date': datetime.now().isoformat(),
                    'complexity_metrics': {'line_count': 1, 'complexity_score': 'low'},
                    'semantic_tags': ['oop_concepts'],
                    'dependencies': {'imports': [], 'function_calls': [], 'includes': [], 'external_libraries': [], 'internal_calls': []},
                    'code_patterns': ['class_definition'],
                    'quality_indicators': {'has_documentation': False, 'maintainability_score': 'fair'},
                    'documentation_info': {'has_comments': False, 'comment_ratio': 0.0},
                    'api_surface': {'visibility': 'public' if 'public' in line else 'internal', 'is_public': 'public' in line},
                    'chunk_id': hashlib.md5(f"{filepath}_{match.group(1)}".encode()).hexdigest()[:8],
                    'method_count': 0
                }
                
                info['classes'].append({
                    'name': match.group(1),
                    'content': line,
                    'start_line': i + 1,
                    'end_line': i + 1,
                    'filepath': str(filepath),
                    'metadata': basic_metadata,
                    'methods': []
                })

        # Extract methods with enhanced metadata
        method_pattern = r'^\s*(?:public|private|protected|internal)?\s*(?:static|virtual|override|abstract)?\s*(?:\w+\s+)?(\w+)\s*\([^)]*\)\s*(?:{|;)'
        for i, line in enumerate(lines):
            match = re.match(method_pattern, line)
            if match and not line.strip().startswith('//'):
                method_name = match.group(1)
                # Skip common non-method keywords
                if method_name not in ['if', 'for', 'while', 'switch', 'using', 'namespace', 'class', 'struct']:
                    basic_metadata = {
                        'type': 'method',
                        'method_name': method_name,
                        'filepath': str(filepath),
                        'relative_path': str(filepath.relative_to(self.repo_path)),
                        'language': 'csharp',
                        'start_line': i + 1,
                        'end_line': i + 1,
                        'processed_date': datetime.now().isoformat(),
                        'complexity_metrics': {'line_count': 1, 'complexity_score': 'low'},
                        'semantic_tags': ['oop_concepts'],
                        'dependencies': {'imports': [], 'function_calls': [], 'includes': [], 'external_libraries': [], 'internal_calls': []},
                        'code_patterns': ['method_definition'],
                        'quality_indicators': {'has_documentation': False, 'maintainability_score': 'fair'},
                        'documentation_info': {'has_comments': False, 'comment_ratio': 0.0},
                        'api_surface': {'visibility': 'public' if 'public' in line else 'private', 'is_public': 'public' in line},
                        'chunk_id': hashlib.md5(f"{filepath}_{method_name}".encode()).hexdigest()[:8]
                    }
                    
                    info['methods'].append({
                        'name': method_name,
                        'content': line,
                        'start_line': i + 1,
                        'end_line': i + 1,
                        'filepath': str(filepath),
                        'metadata': basic_metadata
                    })

        return info

    # Export method for saving enhanced metadata
    
    def export_enhanced_metadata(self, chunks: List[Dict], output_file: str = "enhanced_metadata.json"):
        """Export enhanced metadata to JSON file for analysis."""
        metadata_summary: Dict = {
            'export_date': datetime.now().isoformat(),
            'total_chunks': len(chunks),
            'statistics': {},
            'chunks': []
        }
        
        # Collect statistics
        languages: dict[str, int] = {}
        complexities: dict[str, int] = {}
        semantic_tags: dict[str, int] = {}
        
        for chunk in chunks:
            metadata = chunk['metadata']
            
            # Language stats
            lang = metadata.get('language', 'unknown')
            languages[lang] = languages.get(lang, 0) + 1
            
            # Complexity stats
            complexity = metadata.get('complexity_metrics', {}).get('complexity_score', 'unknown')
            complexities[complexity] = complexities.get(complexity, 0) + 1
            
            # Semantic tag stats
            for tag in metadata.get('semantic_tags', []):
                semantic_tags[tag] = semantic_tags.get(tag, 0) + 1
            
            # Add chunk metadata to export (excluding content for size)
            metadata_summary['chunks'].append({
                'chunk_id': metadata.get('chunk_id'),
                'type': metadata.get('type'),
                'language': metadata.get('language'),
                'complexity_score': metadata.get('complexity_metrics', {}).get('complexity_score'),
                'semantic_tags': metadata.get('semantic_tags', []),
                'quality_score': metadata.get('quality_indicators', {}).get('maintainability_score'),
                'file_path': metadata.get('relative_path'),
                'line_range': f"{metadata.get('start_line', 0)}-{metadata.get('end_line', 0)}"
            })
        
        metadata_summary['statistics'] = {
            'languages': languages,
            'complexity_distribution': complexities,
            'semantic_tags': dict(sorted(semantic_tags.items(), key=lambda x: x[1], reverse=True)[:20])  # Top 20 tags
        }
        
        # Save to file
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(metadata_summary, f, indent=2, ensure_ascii=False)
            print(f"Enhanced metadata exported to: {output_file}")
        except Exception as e:
            print(f"Error exporting metadata: {e}")
        
        return metadata_summary

    # Search optimization methods
    
    def create_searchable_index(self, chunks: List[Dict]) -> Dict:
        """Create searchable indexes based on enhanced metadata."""
        indexes: Dict[str, Dict[str, List[str]]] = {
            'by_semantic_tag': {},
            'by_complexity': {},
            'by_language': {},
            'by_type': {},
            'by_quality': {},
            'by_pattern': {},
            'function_signatures': {},
            'api_surface': {}
        }
        
        for i, chunk in enumerate(chunks):
            metadata = chunk['metadata']
            chunk_id = metadata.get('chunk_id', str(i))
            
            # Index by semantic tags
            for tag in metadata.get('semantic_tags', []):
                if tag not in indexes['by_semantic_tag']:
                    indexes['by_semantic_tag'][tag] = []
                indexes['by_semantic_tag'][tag].append(chunk_id)
            
            # Index by complexity
            complexity = metadata.get('complexity_metrics', {}).get('complexity_score', 'unknown')
            if complexity not in indexes['by_complexity']:
                indexes['by_complexity'][complexity] = []
            indexes['by_complexity'][complexity].append(chunk_id)
            
            # Index by language
            language = metadata.get('language', 'unknown')
            if language not in indexes['by_language']:
                indexes['by_language'][language] = []
            indexes['by_language'][language].append(chunk_id)
            
            # Index by type
            chunk_type = metadata.get('type', 'unknown')
            if chunk_type not in indexes['by_type']:
                indexes['by_type'][chunk_type] = []
            indexes['by_type'][chunk_type].append(chunk_id)
            
            # Index by quality
            quality = metadata.get('quality_indicators', {}).get('maintainability_score', 'unknown')
            if quality not in indexes['by_quality']:
                indexes['by_quality'][quality] = []
            indexes['by_quality'][quality].append(chunk_id)
            
            # Index by patterns
            for pattern in metadata.get('code_patterns', []):
                if pattern not in indexes['by_pattern']:
                    indexes['by_pattern'][pattern] = []
                indexes['by_pattern'][pattern].append(chunk_id)
            
            # Index API surface
            if metadata.get('api_surface', {}).get('is_public', False):
                if 'public' not in indexes['api_surface']:
                    indexes['api_surface']['public'] = []
                indexes['api_surface']['public'].append(chunk_id)
        
        return indexes


# Maintain backward compatibility
CppCodeProcessor = MultiLanguageCodeProcessor