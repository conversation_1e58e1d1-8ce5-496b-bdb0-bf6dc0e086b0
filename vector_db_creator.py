import json
import sys
from typing import List, Dict, Optional
import ollama

try:
    import chromadb
    from chromadb.utils import embedding_functions
except ImportError as e:
    print(f"ChromaDB import error: {e}")
    sys.exit(1)

class OllamaEmbeddingFunction:
    """Enhanced embedding function that uses Ollama for generating embeddings with better error handling"""

    def __init__(self, ollama_host: str = "http://home-ai-server.local:11434", model_name: str = "nomic-embed-text"):
        self.ollama_host = ollama_host
        self.model_name = model_name
        self.client = ollama.Client(host=ollama_host)
        self.embedding_dim = None  # Will be detected on first use

        # Test connection and ensure model is available
        try:
            # Try to pull the embedding model if it's not available
            self.client.pull(model_name)
            print(f"✅ Ollama embedding model '{model_name}' is ready")

            # Test embedding to detect dimensions
            test_response = self.client.embeddings(model=model_name, prompt="test")
            self.embedding_dim = len(test_response.get("embedding", []))
            print(f"✅ Detected embedding dimensions: {self.embedding_dim}")

        except Exception as e:
            print(f"⚠️ Warning: Could not initialize model '{model_name}': {e}")
            print("   Make sure the model is available in Ollama")
            # Default to nomic-embed-text dimensions
            self.embedding_dim = 384

    def __call__(self, input: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts with enhanced error handling"""
        embeddings = []

        for i, text in enumerate(input):
            try:
                # Truncate very long texts to avoid token limits
                if len(text) > 8000:  # Conservative limit
                    text = text[:8000] + "..."
                    print(f"⚠️ Truncated long text for embedding (chunk {i+1})")
                
                response = self.client.embeddings(model=self.model_name, prompt=text)
                embedding = response["embedding"]

                # Update dimension if not set
                if self.embedding_dim is None:
                    self.embedding_dim = len(embedding)
                    print(f"✅ Detected embedding dimensions: {self.embedding_dim}")

                embeddings.append(embedding)
                
                # Progress indicator for large batches
                if (i + 1) % 10 == 0:
                    print(f"   Generated embeddings for {i + 1}/{len(input)} texts")
                    
            except Exception as e:
                print(f"❌ Error generating embedding for text {i+1}: {e}")
                # Return a zero vector as fallback with correct dimensions
                fallback_dim = self.embedding_dim if self.embedding_dim else 384
                embeddings.append([0.0] * fallback_dim)

        return embeddings

class EnhancedVectorDBCreator:
    """Enhanced vector database creator with support for rich metadata and optimized processing"""
    
    def __init__(self, db_path="./chroma_db", ollama_host="http://home-ai-server.local:11434", use_ollama=True):
        """Initialize the enhanced vector database creator with optional Ollama embeddings"""
        try:
            self.client = chromadb.PersistentClient(path=db_path)
            self.collection_name = "enhanced_code_collection"  # Default, will be updated
            self.use_ollama = use_ollama
            self.ollama_host = ollama_host
            
            # Set up embedding function
            if use_ollama:
                print(f"🔥 Initializing Enhanced VectorDB with Ollama embeddings (host: {ollama_host})")
                self.embedding_function = OllamaEmbeddingFunction(
                    ollama_host=ollama_host,
                    model_name="nomic-embed-text"  # Good embedding model for code
                )
            else:
                print("🔥 Using ChromaDB default embeddings for Enhanced VectorDB")
                self.embedding_function = embedding_functions.DefaultEmbeddingFunction()
            
            print(f"Initialized Enhanced ChromaDB client at {db_path}")
        except Exception as e:
            print(f"Error initializing Enhanced ChromaDB client: {e}")
            raise
    
    def create_collection(self, chunks: List[Dict], collection_name: Optional[str] = None):
        """Create ChromaDB collection with enhanced metadata support and optimized processing"""
        
        if collection_name:
            self.collection_name = collection_name
        
        # Delete existing collection if it exists
        try:
            self.client.delete_collection(self.collection_name)
            print(f"Deleted existing collection: {self.collection_name}")
        except Exception:
            print(f"No existing collection to delete: {self.collection_name}")
        
        # Create new collection with enhanced metadata
        try:
            collection = self.client.create_collection(
                name=self.collection_name,
                embedding_function=self.embedding_function,
                metadata={
                    "description": "Enhanced multi-language code with semantic metadata, quality analysis, and complexity metrics",
                    "version": "3.0_enhanced",
                    "features": "semantic_tags,quality_analysis,complexity_metrics,api_surface_analysis",
                    "languages": "C,C++,Python,C#,JavaScript,TypeScript,Rust,Java,Go,SQL,TCL,Verilog,Bash,CommonLisp,EmacsLisp,Scheme,Lua,Make,JSON,YAML,XML,PHP,Perl,Markdown,HTML,Fortran,VHDL"
                }
            )
            print(f"Created enhanced collection: {self.collection_name}")
            if self.use_ollama:
                print("Using Ollama embeddings with nomic-embed-text model")
            else:
                print("Using ChromaDB's default embedding function")
        except Exception as e:
            print(f"Error creating enhanced collection: {e}")
            raise
        
        # Prepare data for insertion with enhanced content formatting
        documents = []
        metadatas = []
        ids = []
        
        print(f"Preparing {len(chunks)} chunks with enhanced metadata...")
        
        for i, chunk in enumerate(chunks):
            try:
                # Enhanced document content with rich metadata context
                content = self._format_enhanced_document_content(chunk)
                documents.append(content)
                
                # Ensure metadata is ChromaDB compatible with enhanced fields
                metadata = self._flatten_enhanced_metadata(chunk['metadata'])
                metadatas.append(metadata)
                
                # Create more descriptive IDs with enhanced information
                chunk_id = chunk['metadata'].get('chunk_id', f'chunk_{i}')
                chunk_type = chunk['metadata'].get('type', 'unknown')
                language = chunk['metadata'].get('language', 'unknown')
                ids.append(f"{language}_{chunk_type}_{i}_{chunk_id}")
                
            except Exception as e:
                print(f"❌ Error preparing chunk {i}: {e}")
                # Skip problematic chunks but continue processing
                continue
        
        print(f"Adding {len(documents)} enhanced documents to collection...")
        if self.use_ollama:
            print("Generating embeddings using Ollama (this may take a while for large codebases)...")
        else:
            print("ChromaDB will automatically generate embeddings")
        
        # Show enhanced statistics before insertion
        self._show_enhanced_chunk_statistics(chunks)
        
        # Process in smaller batches for enhanced metadata (more processing per chunk)
        batch_size = 25 if self.use_ollama else 50  # Smaller batches for Ollama due to enhanced content
        total_batches = (len(documents) - 1) // batch_size + 1
        successful_batches = 0
        failed_chunks = []
        total_processed = 0
        
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i+batch_size]
            batch_metas = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]
            
            batch_num = i // batch_size + 1
            print(f"Processing enhanced batch {batch_num}/{total_batches} ({len(batch_docs)} documents)")
            
            try:
                # ChromaDB will use our enhanced embedding function
                collection.add(
                    documents=batch_docs,
                    metadatas=batch_metas,
                    ids=batch_ids
                )
                print(f"✓ Successfully added enhanced batch {batch_num}")
                successful_batches += 1
                total_processed += len(batch_docs)
                
                # Progress indicator
                progress = (total_processed / len(documents)) * 100
                print(f"   Progress: {progress:.1f}% ({total_processed}/{len(documents)} documents)")
                
            except Exception as e:
                print(f"✗ Error processing enhanced batch {batch_num}: {e}")
                # Track failed chunks for debugging
                for j, chunk_id in enumerate(batch_ids):
                    failed_chunks.append({
                        'id': chunk_id,
                        'error': str(e)[:200],  # Truncate long errors
                        'batch': batch_num,
                        'metadata_keys': list(batch_metas[j].keys()) if j < len(batch_metas) else []
                    })
                continue
        
        final_count = collection.count()
        print("✅ Enhanced collection created successfully!")
        print(f"   Successful batches: {successful_batches}/{total_batches}")
        print(f"   Total documents in collection: {final_count:,}")
        print("   Enhanced features: Semantic tags, quality analysis, complexity metrics")
        
        if failed_chunks:
            print(f"⚠ Warning: {len(failed_chunks)} chunks failed to insert")
            # Save failed chunks for debugging
            try:
                with open(f"failed_chunks_{self.collection_name}.json", "w") as f:
                    json.dump(failed_chunks, f, indent=2)
                print(f"Failed chunk details saved to failed_chunks_{self.collection_name}.json")
            except Exception as e:
                print(f"Could not save failed chunks file: {e}")
        
        # Validate enhanced metadata in collection
        self._validate_enhanced_metadata(collection)
        
        return collection
    
    def _format_enhanced_document_content(self, chunk: Dict) -> str:
        """Format document content with rich metadata context for enhanced semantic search"""
        metadata = chunk['metadata']
        content = chunk['content']
        
        # Enhanced contextual information for better embeddings
        context_parts = []
        
        # Add file and location context
        rel_path = metadata.get('relative_path', metadata.get('filepath', ''))
        if rel_path:
            context_parts.append(f"File: {rel_path}")
        
        # Add line range for precise location
        start_line = metadata.get('start_line')
        end_line = metadata.get('end_line')
        if start_line and end_line:
            context_parts.append(f"Lines: {start_line}-{end_line}")
        
        # Add language and type context
        language = metadata.get('language', 'unknown')
        chunk_type = metadata.get('type', 'unknown')
        context_parts.append(f"Language: {language.upper()}")
        context_parts.append(f"Type: {chunk_type}")
        
        # Add enhanced metadata context
        
        # Semantic tags for domain context
        semantic_tags = metadata.get('semantic_tags', [])
        if isinstance(semantic_tags, str):
            try:
                semantic_tags = json.loads(semantic_tags)
            except (json.JSONDecodeError, ValueError):
                semantic_tags = []
        if semantic_tags:
            context_parts.append(f"Domains: {', '.join(semantic_tags[:3])}")  # Top 3 tags
        
        # Quality indicators
        quality_indicators = metadata.get('quality_indicators', {})
        if isinstance(quality_indicators, str):
            try:
                quality_indicators = json.loads(quality_indicators)
            except (json.JSONDecodeError, ValueError, TypeError):
                quality_indicators = {}
        
        quality_score = quality_indicators.get('maintainability_score', 'unknown')
        if quality_score != 'unknown':
            context_parts.append(f"Quality: {quality_score}")
        
        # Complexity information
        complexity_metrics = metadata.get('complexity_metrics', {})
        if isinstance(complexity_metrics, str):
            try:
                complexity_metrics = json.loads(complexity_metrics)
            except json.JSONDecodeError:
                complexity_metrics = {}
        
        complexity_score = complexity_metrics.get('complexity_score', 'unknown')
        if complexity_score != 'unknown':
            context_parts.append(f"Complexity: {complexity_score}")
        
        # API surface information
        api_surface = metadata.get('api_surface', {})
        if isinstance(api_surface, str):
            try:
                api_surface = json.loads(api_surface)
            except (json.JSONDecodeError, ValueError):
                api_surface = {}
        
        visibility = api_surface.get('visibility', 'unknown')
        if visibility != 'unknown':
            context_parts.append(f"Visibility: {visibility}")
        
        # Add specific identifiers with enhanced context
        if chunk_type == 'function' and 'function_name' in metadata:
            function_name = metadata['function_name']
            context_parts.append(f"Function: {function_name}")
            
            # Add parameter information if available
            param_count = complexity_metrics.get('parameter_count', 0)
            if param_count > 0:
                context_parts.append(f"Parameters: {param_count}")
                
        elif chunk_type == 'class' and 'class_name' in metadata:
            class_name = metadata['class_name']
            context_parts.append(f"Class: {class_name}")
            
            # Add method count if available
            method_count = metadata.get('method_count', 0)
            if method_count > 0:
                context_parts.append(f"Methods: {method_count}")
                
        elif chunk_type == 'method':
            class_name = metadata.get('class_name', 'Unknown')
            method_name = metadata.get('method_name', 'Unknown')
            context_parts.append(f"Method: {class_name}::{method_name}")
            
        elif chunk_type == 'namespace' and 'namespace_name' in metadata:
            namespace_name = metadata['namespace_name']
            context_parts.append(f"Namespace: {namespace_name}")
        
        # Add code patterns for better searchability
        code_patterns = metadata.get('code_patterns', [])
        if isinstance(code_patterns, str):
            try:
                code_patterns = json.loads(code_patterns)
            except (json.JSONDecodeError, ValueError):
                code_patterns = []
        if code_patterns:
            context_parts.append(f"Patterns: {', '.join(code_patterns[:2])}")  # Top 2 patterns
        
        # Combine enhanced context with content
        context_header = " | ".join(context_parts)
        
        # Create enhanced document with structured metadata
        enhanced_content = f"""=== CODE METADATA ===
{context_header}

=== SOURCE CODE ===
{content}

=== SEARCHABLE TERMS ==="""
        
        # Add searchable terms based on metadata
        searchable_terms = []
        
        # Add function/class names as searchable terms
        if 'function_name' in metadata:
            searchable_terms.append(f"function_{metadata['function_name']}")
        if 'class_name' in metadata:
            searchable_terms.append(f"class_{metadata['class_name']}")
        if 'method_name' in metadata:
            searchable_terms.append(f"method_{metadata['method_name']}")
        
        # Add semantic tags as searchable terms
        for tag in semantic_tags:
            searchable_terms.append(f"domain_{tag}")
        
        # Add quality and complexity as searchable terms
        if quality_score != 'unknown':
            searchable_terms.append(f"quality_{quality_score}")
        if complexity_score != 'unknown':
            searchable_terms.append(f"complexity_{complexity_score}")
        
        # Add language-specific searchable terms
        if language == 'c':
            if any(keyword in content.lower() for keyword in ['malloc', 'free']):
                searchable_terms.append("memory_management")
            if any(keyword in content.lower() for keyword in ['socket', 'bind', 'listen']):
                searchable_terms.append("network_programming")
        elif language == 'cpp':
            if any(keyword in content.lower() for keyword in ['new', 'delete', 'malloc', 'free']):
                searchable_terms.append("memory_management")
            if any(keyword in content.lower() for keyword in ['class', 'template', 'namespace']):
                searchable_terms.append("oop_features")
            if any(keyword in content.lower() for keyword in ['socket', 'bind', 'listen']):
                searchable_terms.append("network_programming")
        elif language == 'python':
            if any(keyword in content.lower() for keyword in ['async', 'await']):
                searchable_terms.append("async_programming")
            if any(keyword in content.lower() for keyword in ['import', 'from']):
                searchable_terms.append("module_usage")
        elif language == 'csharp':
            if any(keyword in content.lower() for keyword in ['linq', 'select', 'where']):
                searchable_terms.append("linq_usage")
            if any(keyword in content.lower() for keyword in ['async', 'task']):
                searchable_terms.append("async_programming")
        elif language == 'javascript':
            if any(keyword in content.lower() for keyword in ['async', 'await', 'promise']):
                searchable_terms.append("async_programming")
            if any(keyword in content.lower() for keyword in ['react', 'component', 'jsx']):
                searchable_terms.append("react_framework")
            if any(keyword in content.lower() for keyword in ['node', 'express', 'server']):
                searchable_terms.append("nodejs_backend")
        elif language == 'typescript':
            if any(keyword in content.lower() for keyword in ['interface', 'type', 'generic']):
                searchable_terms.append("type_system")
            if any(keyword in content.lower() for keyword in ['async', 'await', 'promise']):
                searchable_terms.append("async_programming")
            if any(keyword in content.lower() for keyword in ['react', 'component', 'tsx']):
                searchable_terms.append("react_framework")
        elif language == 'rust':
            if any(keyword in content.lower() for keyword in ['unsafe', 'raw', 'ptr']):
                searchable_terms.append("unsafe_code")
            if any(keyword in content.lower() for keyword in ['async', 'await', 'tokio']):
                searchable_terms.append("async_programming")
            if any(keyword in content.lower() for keyword in ['trait', 'impl', 'generic']):
                searchable_terms.append("trait_system")
        elif language == 'java':
            if any(keyword in content.lower() for keyword in ['spring', 'annotation', '@']):
                searchable_terms.append("spring_framework")
            if any(keyword in content.lower() for keyword in ['thread', 'concurrent', 'executor']):
                searchable_terms.append("concurrency")
            if any(keyword in content.lower() for keyword in ['interface', 'abstract', 'extends', 'implements', 'class']):
                searchable_terms.append("oop_patterns")
        elif language == 'go':
            if any(keyword in content.lower() for keyword in ['goroutine', 'channel', 'select', 'go ', 'chan', 'make(chan']):
                searchable_terms.append("concurrency")
            if any(keyword in content.lower() for keyword in ['gin', 'echo', 'fiber', 'net/http']):
                searchable_terms.append("web_framework")
            if any(keyword in content.lower() for keyword in ['context', 'sync', 'mutex']):
                searchable_terms.append("synchronization")
        elif language == 'sql':
            if any(keyword in content.lower() for keyword in ['select', 'join', 'where']):
                searchable_terms.append("query_operations")
            if any(keyword in content.lower() for keyword in ['create', 'alter', 'drop']):
                searchable_terms.append("ddl_operations")
            if any(keyword in content.lower() for keyword in ['insert', 'update', 'delete']):
                searchable_terms.append("dml_operations")
        elif language == 'bash':
            if any(keyword in content.lower() for keyword in ['function', 'if', 'for', 'while']):
                searchable_terms.append("shell_scripting")
            if any(keyword in content.lower() for keyword in ['grep', 'sed', 'awk']):
                searchable_terms.append("text_processing")
        elif language == 'php':
            if any(keyword in content.lower() for keyword in ['class', 'extends', 'implements']):
                searchable_terms.append("oop_patterns")
            if any(keyword in content.lower() for keyword in ['$_get', '$_post', '$_session']):
                searchable_terms.append("web_development")
        elif language == 'html':
            if any(keyword in content.lower() for keyword in ['form', 'input', 'button']):
                searchable_terms.append("forms")
            if any(keyword in content.lower() for keyword in ['div', 'span', 'section']):
                searchable_terms.append("layout")
        elif language == 'verilog':
            if any(keyword in content.lower() for keyword in ['always', 'posedge', 'negedge']):
                searchable_terms.append("sequential_logic")
            if any(keyword in content.lower() for keyword in ['assign', 'wire']):
                searchable_terms.append("combinational_logic")
        elif language == 'vhdl':
            if any(keyword in content.lower() for keyword in ['process', 'clk', 'reset']):
                searchable_terms.append("sequential_logic")
            if any(keyword in content.lower() for keyword in ['entity', 'architecture']):
                searchable_terms.append("design_units")
            if any(keyword in content.lower() for keyword in ['signal', 'variable', 'port']):
                searchable_terms.append("signal_processing")
        elif language == 'tcl':
            if any(keyword in content.lower() for keyword in ['proc', 'namespace']):
                searchable_terms.append("procedures")
            if any(keyword in content.lower() for keyword in ['string', 'regexp', 'list']):
                searchable_terms.append("data_processing")
        elif language == 'commonlisp':
            if any(keyword in content.lower() for keyword in ['defun', 'lambda']):
                searchable_terms.append("functional_programming")
            if any(keyword in content.lower() for keyword in ['defmacro', 'macro']):
                searchable_terms.append("macro_programming")
            if any(keyword in content.lower() for keyword in ['defclass', 'defmethod']):
                searchable_terms.append("object_oriented")
        elif language == 'elisp':
            if any(keyword in content.lower() for keyword in ['defun', 'lambda']):
                searchable_terms.append("functional_programming")
            if any(keyword in content.lower() for keyword in ['buffer', 'current-buffer']):
                searchable_terms.append("buffer_management")
            if any(keyword in content.lower() for keyword in ['hook', 'keymap']):
                searchable_terms.append("emacs_customization")
        elif language == 'scheme':
            if any(keyword in content.lower() for keyword in ['define', 'lambda']):
                searchable_terms.append("functional_programming")
            if any(keyword in content.lower() for keyword in ['call/cc', 'continuation']):
                searchable_terms.append("continuation_programming")
        elif language == 'lua':
            if any(keyword in content.lower() for keyword in ['function', 'local']):
                searchable_terms.append("scripting")
            if any(keyword in content.lower() for keyword in ['table', 'metatable']):
                searchable_terms.append("table_programming")
            if any(keyword in content.lower() for keyword in ['coroutine', 'yield']):
                searchable_terms.append("coroutine_programming")
        elif language == 'make':
            if any(keyword in content.lower() for keyword in ['target', 'phony']):
                searchable_terms.append("build_automation")
            if any(keyword in content.lower() for keyword in ['ifdef', 'ifndef', 'ifeq']):
                searchable_terms.append("conditional_build")
        elif language == 'json':
            if any(keyword in content.lower() for keyword in ['object', 'array']):
                searchable_terms.append("data_structure")
            if any(keyword in content.lower() for keyword in ['schema', 'validation']):
                searchable_terms.append("data_validation")
        elif language == 'yaml':
            if any(keyword in content.lower() for keyword in ['mapping', 'sequence']):
                searchable_terms.append("data_structure")
            if any(keyword in content.lower() for keyword in ['anchor', 'alias']):
                searchable_terms.append("yaml_features")
        elif language == 'xml':
            if any(keyword in content.lower() for keyword in ['element', 'attribute']):
                searchable_terms.append("markup_structure")
            if any(keyword in content.lower() for keyword in ['namespace', 'schema']):
                searchable_terms.append("xml_features")
        elif language == 'perl':
            if any(keyword in content.lower() for keyword in ['my', 'our', 'local']):
                searchable_terms.append("variable_scoping")
            if any(keyword in content.lower() for keyword in ['regex', 'match', 'substitute', '=~', 's/', 'm/']):
                searchable_terms.append("regex_processing")
            if any(keyword in content.lower() for keyword in ['ref', 'deref', '\\$', '\\@', '\\%']):
                searchable_terms.append("reference_programming")
        elif language == 'markdown':
            if any(keyword in content.lower() for keyword in ['header', 'h1', 'h2']):
                searchable_terms.append("documentation_structure")
            if any(keyword in content.lower() for keyword in ['link', 'image', 'code']):
                searchable_terms.append("content_formatting")
        elif language == 'fortran':
            if any(keyword in content.lower() for keyword in ['program', 'subroutine', 'function']):
                searchable_terms.append("program_units")
            if any(keyword in content.lower() for keyword in ['module', 'use', 'interface']):
                searchable_terms.append("modular_programming")
            if any(keyword in content.lower() for keyword in ['real', 'complex', 'dimension']):
                searchable_terms.append("scientific_computing")

        if searchable_terms:
            enhanced_content += f"\n{' '.join(searchable_terms)}"
        
        return enhanced_content
    
    def _flatten_enhanced_metadata(self, metadata: Dict) -> Dict:
        """Flatten enhanced metadata to ensure ChromaDB compatibility while preserving rich information"""
        flattened = {}
        
        # Enhanced metadata flattening with better handling
        for key, value in metadata.items():
            if isinstance(value, (str, int, float, bool)):
                flattened[key] = value
            elif value is None:
                flattened[key] = ""
            elif isinstance(value, (list, dict)):
                # Convert complex types to JSON strings with error handling
                try:
                    flattened[key] = json.dumps(value, ensure_ascii=False)
                except Exception as e:
                    print(f"⚠️ Warning: Could not serialize metadata field '{key}': {e}")
                    flattened[key] = str(value)[:500]  # Truncate long values
            else:
                # Convert other types to strings with length limit
                str_value = str(value)
                flattened[key] = str_value[:500] if len(str_value) > 500 else str_value
        
        # Ensure required fields exist
        required_fields = ['type', 'language', 'relative_path']
        for field in required_fields:
            if field not in flattened:
                flattened[field] = 'unknown'
        
        # Add enhanced metadata indicators
        flattened['has_enhanced_metadata'] = True
        flattened['metadata_version'] = '3.0_enhanced'
        
        return flattened
    
    def _show_enhanced_chunk_statistics(self, chunks: List[Dict]):
        """Display comprehensive statistics about the enhanced chunks being processed"""
        print("\n=== Enhanced Chunk Statistics ===")
        
        # Enhanced counters
        type_counts: Dict[str, int] = {}
        language_counts: Dict[str, int] = {}
        file_counts: Dict[str, int] = {}
        complexity_counts: Dict[str, int] = {}
        quality_counts: Dict[str, int] = {}
        semantic_tag_frequency: Dict[str, int] = {}
        pattern_frequency: Dict[str, int] = {}
        
        total_lines = 0
        documented_chunks = 0
        public_api_chunks = 0
        
        for chunk in chunks:
            metadata = chunk['metadata']
            
            # Basic counts
            chunk_type = metadata.get('type', 'unknown')
            type_counts[chunk_type] = type_counts.get(chunk_type, 0) + 1
            
            language = metadata.get('language', 'unknown')
            language_counts[language] = language_counts.get(language, 0) + 1
            
            filepath = metadata.get('relative_path', metadata.get('filepath', 'unknown'))
            file_counts[filepath] = file_counts.get(filepath, 0) + 1
            
            # Enhanced metadata analysis
            
            # Complexity analysis
            complexity_metrics = metadata.get('complexity_metrics', {})
            if isinstance(complexity_metrics, dict):
                complexity_score = complexity_metrics.get('complexity_score', 'unknown')
                complexity_counts[complexity_score] = complexity_counts.get(complexity_score, 0) + 1
                
                # Line count
                line_count = complexity_metrics.get('line_count', 0)
                total_lines += line_count
            
            # Quality analysis
            quality_indicators = metadata.get('quality_indicators', {})
            if isinstance(quality_indicators, dict):
                quality_score = quality_indicators.get('maintainability_score', 'unknown')
                quality_counts[quality_score] = quality_counts.get(quality_score, 0) + 1
                
                # Documentation tracking
                if quality_indicators.get('has_documentation', False):
                    documented_chunks += 1
            
            # Semantic tags analysis
            semantic_tags = metadata.get('semantic_tags', [])
            if isinstance(semantic_tags, list):
                for tag in semantic_tags:
                    semantic_tag_frequency[tag] = semantic_tag_frequency.get(tag, 0) + 1
            
            # Code patterns analysis
            code_patterns = metadata.get('code_patterns', [])
            if isinstance(code_patterns, list):
                for pattern in code_patterns:
                    pattern_frequency[pattern] = pattern_frequency.get(pattern, 0) + 1
            
            # API surface analysis
            api_surface = metadata.get('api_surface', {})
            if isinstance(api_surface, dict):
                if api_surface.get('is_public', False):
                    public_api_chunks += 1
        
        print(f"Total enhanced chunks: {len(chunks):,}")
        print(f"Total lines of code: {total_lines:,}")
        if len(chunks) > 0:
            print(f"Average chunk size: {total_lines / len(chunks):.1f} lines")
        else:
            print("Average chunk size: N/A (no chunks processed)")
        
        print("\n📊 Enhanced Metadata Coverage:")
        if len(chunks) > 0:
            print(f"  Documented chunks: {documented_chunks:,} ({documented_chunks/len(chunks)*100:.1f}%)")
            print(f"  Public API chunks: {public_api_chunks:,} ({public_api_chunks/len(chunks)*100:.1f}%)")
        else:
            print("  No chunks available for metadata analysis")
        
        print("\n🏗️ Code Structure Types:")
        if len(chunks) > 0:
            for chunk_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / len(chunks)) * 100
                print(f"  {chunk_type}: {count:,} ({percentage:.1f}%)")
        else:
            print("  No code structure types detected")
        
        print("\n💻 Programming Languages:")
        if len(chunks) > 0:
            for language, count in sorted(language_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / len(chunks)) * 100
                print(f"  {language.upper()}: {count:,} ({percentage:.1f}%)")
        else:
            print("  No programming languages detected")
        
        if complexity_counts:
            print("\n⚡ Complexity Distribution:")
            complexity_order = ['low', 'medium', 'high', 'very_high', 'unknown']
            for complexity in complexity_order:
                if complexity in complexity_counts:
                    count = complexity_counts[complexity]
                    if len(chunks) > 0:
                        percentage = (count / len(chunks)) * 100
                        emoji = {'low': '🟢', 'medium': '🟡', 'high': '🟠', 'very_high': '🔴', 'unknown': '⚪'}[complexity]
                        print(f"  {emoji} {complexity}: {count:,} ({percentage:.1f}%)")
                    else:
                        emoji = {'low': '🟢', 'medium': '🟡', 'high': '🟠', 'very_high': '🔴', 'unknown': '⚪'}[complexity]
                        print(f"  {emoji} {complexity}: {count:,} (N/A%)")
        
        if quality_counts:
            print("\n✨ Quality Distribution:")
            quality_order = ['excellent', 'good', 'fair', 'poor', 'unknown']
            for quality in quality_order:
                if quality in quality_counts:
                    count = quality_counts[quality]
                    if len(chunks) > 0:
                        percentage = (count / len(chunks)) * 100
                        emoji = {'excellent': '✨', 'good': '🟢', 'fair': '🟡', 'poor': '🔴', 'unknown': '⚪'}[quality]
                        print(f"  {emoji} {quality}: {count:,} ({percentage:.1f}%)")
                    else:
                        emoji = {'excellent': '✨', 'good': '🟢', 'fair': '🟡', 'poor': '🔴', 'unknown': '⚪'}[quality]
                        print(f"  {emoji} {quality}: {count:,} (N/A%)")
        
        if semantic_tag_frequency:
            print("\n🏷️ Top Semantic Categories:")
            sorted_tags = sorted(semantic_tag_frequency.items(), key=lambda x: x[1], reverse=True)
            for tag, count in sorted_tags[:10]:  # Top 10 tags
                if len(chunks) > 0:
                    percentage = (count / len(chunks)) * 100
                    display_tag = tag.replace('_', ' ').title()
                    print(f"  • {display_tag}: {count:,} ({percentage:.1f}%)")
                else:
                    display_tag = tag.replace('_', ' ').title()
                    print(f"  • {display_tag}: {count:,} (N/A%)")
        
        if pattern_frequency:
            print("\n🎨 Top Code Patterns:")
            sorted_patterns = sorted(pattern_frequency.items(), key=lambda x: x[1], reverse=True)
            for pattern, count in sorted_patterns[:8]:  # Top 8 patterns
                if len(chunks) > 0:
                    percentage = (count / len(chunks)) * 100
                    display_pattern = pattern.replace('_', ' ').title()
                    print(f"  • {display_pattern}: {count:,} ({percentage:.1f}%)")
                else:
                    display_pattern = pattern.replace('_', ' ').title()
                    print(f"  • {display_pattern}: {count:,} (N/A%)")
        
        print(f"\n📁 Files processed: {len(file_counts):,}")
        print("=" * 50 + "\n")
    
    def _validate_enhanced_metadata(self, collection):
        """Validate that enhanced metadata was properly stored in the collection"""
        print("🔍 Validating enhanced metadata in collection...")
        
        try:
            # Get a sample to check metadata structure
            sample = collection.get(limit=3, include=['metadatas'])
            
            if not sample['metadatas']:
                print("⚠️ Warning: No metadata found in collection")
                return
            
            enhanced_features_found = set()
            metadata_issues = []
            
            for i, metadata in enumerate(sample['metadatas']):
                sample_id = f"sample_{i+1}"
                
                # Check for enhanced metadata fields
                enhanced_fields = [
                    'semantic_tags', 'complexity_metrics', 'quality_indicators',
                    'code_patterns', 'api_surface', 'dependencies'
                ]
                
                found_fields = [field for field in enhanced_fields if field in metadata]
                enhanced_features_found.update(found_fields)
                
                # Check metadata version
                metadata_version = metadata.get('metadata_version', 'unknown')
                has_enhanced_flag = metadata.get('has_enhanced_metadata', False)
                
                if not has_enhanced_flag:
                    metadata_issues.append(f"{sample_id}: Missing enhanced metadata flag")
                
                if metadata_version != '3.0_enhanced':
                    metadata_issues.append(f"{sample_id}: Unexpected metadata version: {metadata_version}")
                
                # Validate JSON fields can be parsed
                for field in ['semantic_tags', 'complexity_metrics', 'quality_indicators']:
                    if field in metadata:
                        try:
                            if isinstance(metadata[field], str):
                                json.loads(metadata[field])
                        except json.JSONDecodeError:
                            metadata_issues.append(f"{sample_id}: Invalid JSON in {field}")
            
            # Report validation results
            print("✅ Validation complete:")
            print(f"   Enhanced features found: {len(enhanced_features_found)}/6")
            print(f"   Features detected: {', '.join(sorted(enhanced_features_found))}")
            
            if metadata_issues:
                print("⚠️ Validation issues found:")
                for issue in metadata_issues:
                    print(f"   • {issue}")
            else:
                print("✅ All enhanced metadata validated successfully")
                
        except Exception as e:
            print(f"❌ Error during metadata validation: {e}")

# Maintain backward compatibility
VectorDBCreator = EnhancedVectorDBCreator