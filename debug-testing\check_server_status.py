#!/usr/bin/env python3
"""
Check if the Code Analysis server is running and diagnose startup issues
"""

import requests
import time
import subprocess
import sys

CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def check_server_connectivity():
    """Check if the server is responding"""
    print("🔍 Checking server connectivity...")
    
    for attempt in range(5):
        try:
            print(f"   Attempt {attempt + 1}/5...")
            response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Server is responding!")
                data = response.json()
                print(f"   Version: {data.get('version', 'unknown')}")
                return True
            else:
                print(f"   Server responded with status {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"   Connection refused (attempt {attempt + 1})")
        except requests.exceptions.Timeout:
            print(f"   Timeout (attempt {attempt + 1})")
        except Exception as e:
            print(f"   Error: {e}")
        
        if attempt < 4:  # Don't wait after the last attempt
            print("   Waiting 3 seconds before retry...")
            time.sleep(3)
    
    print("❌ Server is not responding")
    return False

def check_docker_status():
    """Check Docker container status on home-ai-server"""
    print("\n🐳 Checking Docker container status on home-ai-server...")

    try:
        # Check if docker is available on remote server
        result = subprocess.run(['ssh', 'home-ai-server', 'docker', '--version'],
                              capture_output=True, text=True, timeout=15)
        if result.returncode != 0:
            print("❌ Docker is not available on home-ai-server (or SSH failed)")
            print(f"   Error: {result.stderr.strip()}")
            return False

        print("✅ Docker is available on home-ai-server")

        # Check container status on remote server
        result = subprocess.run(['ssh', 'home-ai-server', 'docker', 'ps', '--filter', 'name=code-analyzer-server'],
                              capture_output=True, text=True, timeout=15)

        if result.returncode == 0:
            output = result.stdout.strip()
            if output and len(output.split('\n')) > 1:  # More than just header
                print("📋 Container status on home-ai-server:")
                print(output)

                if "code-analyzer-server" in output:
                    if "Up" in output:
                        print("✅ Code Analysis server container is running on home-ai-server")
                        return True
                    else:
                        print("❌ Code Analysis server container is not running on home-ai-server")
                        return False
                else:
                    print("❌ Code Analysis server container not found on home-ai-server")
                    return False
            else:
                print("❌ No containers found matching 'code-analyzer-server' on home-ai-server")
                return False
        else:
            print(f"❌ Docker ps failed on home-ai-server: {result.stderr.strip()}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ SSH/Docker command timed out")
        print("💡 Falling back to indirect Docker status check...")
        return check_docker_status_indirect()
    except FileNotFoundError:
        print("❌ SSH command not found (SSH client not installed)")
        print("💡 Falling back to indirect Docker status check...")
        return check_docker_status_indirect()
    except Exception as e:
        print(f"❌ Docker check failed: {e}")
        print("💡 Falling back to indirect Docker status check...")
        return check_docker_status_indirect()

def check_docker_status_indirect():
    """Indirect Docker status check by testing Code Analysis server connectivity"""
    print("🔍 Checking if Code Analysis server is accessible (indirect Docker check)...")

    try:
        # If we can reach the Code Analysis server, Docker is likely running
        response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/health", timeout=10)
        if response.status_code == 200:
            print("✅ Code Analysis server is accessible (Docker likely running)")
            return True
        else:
            print(f"❌ Code Analysis server returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Code Analysis server (Docker likely not running)")
        return False
    except requests.exceptions.Timeout:
        print("❌ Code Analysis server connection timed out")
        return False
    except Exception as e:
        print(f"❌ Code Analysis server check failed: {e}")
        return False

def check_container_logs():
    """Check container logs for errors"""
    print("\n📋 Checking container logs...")
    
    try:
        result = subprocess.run(['docker', 'logs', '--tail', '20', 'code-analyzer-server'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            logs = result.stdout.strip()
            if logs:
                print("📄 Recent logs:")
                print("-" * 50)
                print(logs)
                print("-" * 50)
                
                # Look for common error patterns
                if "error" in logs.lower() or "exception" in logs.lower():
                    print("⚠️ Errors found in logs")
                elif "started server" in logs.lower() or "uvicorn" in logs.lower():
                    print("✅ Server appears to have started")
                else:
                    print("❓ No clear startup indicators in logs")
            else:
                print("📄 No recent logs found")
        else:
            print(f"❌ Failed to get logs: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Log check failed: {e}")

def suggest_fixes():
    """Suggest potential fixes"""
    print("\n🔧 SUGGESTED FIXES:")
    print("=" * 50)
    
    print("1. **Restart the Code Analysis server container:**")
    print("   docker-compose restart code-analyzer-server")
    print()
    
    print("2. **Check for build errors:**")
    print("   docker-compose logs code-analyzer-server")
    print()
    
    print("3. **Rebuild the container:**")
    print("   docker-compose down")
    print("   docker-compose up --build -d code-analyzer-server")
    print()
    
    print("4. **Check if port 5002 is blocked:**")
    print("   netstat -an | findstr :5002")
    print()
    
    print("5. **Full system restart:**")
    print("   docker-compose down")
    print("   docker-compose up -d")

def main():
    print("🚀 Code Analysis Server Status Checker")
    print("=" * 50)
    print("ℹ️  This tool checks Docker status on home-ai-server (remote)")
    print("   If SSH is not configured, it will fall back to indirect checks")
    print()

    # Check server connectivity
    server_running = check_server_connectivity()

    # Check Docker status
    docker_ok = check_docker_status()
    
    # If Docker is OK but server not responding, check logs
    if docker_ok and not server_running:
        check_container_logs()
    
    # Provide suggestions
    if not server_running:
        suggest_fixes()
    else:
        print("\n✅ Server is running normally!")
        print("You can now try creating vector stores again.")

if __name__ == "__main__":
    main()
