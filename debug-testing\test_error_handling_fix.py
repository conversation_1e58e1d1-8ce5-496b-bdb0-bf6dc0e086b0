#!/usr/bin/env python3
"""
Test the error handling fix for select_codebase
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_error_handling_fix():
    """Test that the error handling fix works"""
    print("🧪 Testing Error Handling Fix")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test the specific failing query from auto-tester
    query = "select codebase utils"
    
    print(f"🔍 Testing: '{query}'")
    print("-" * 30)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": query}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 800
            },
            timeout=90
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ Response received ({len(content)} chars)")
            
            # Check for the specific problematic content from auto-tester
            problematic_phrases = [
                "A Python error!",
                "Tools.select_codebase()",
                "missing required positional argument",
                "import code_analyzer_tools",
                "Replace `'my_codebase'`"
            ]
            
            found_issues = [phrase for phrase in problematic_phrases if phrase in content]
            
            print(f"\n📋 Analysis:")
            if found_issues:
                print(f"❌ STILL HAS ISSUES: {found_issues}")
                print("🔧 Tool needs to be updated in OpenWebUI")
            else:
                print("✅ NO INTERNAL DETAILS FOUND")
                print("🎉 Error handling fix is working!")
            
            print(f"\n📄 Full Response:")
            print("-" * 40)
            print(content)
            print("-" * 40)
            
            return len(found_issues) == 0
            
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 Error Handling Fix Test")
    print("=" * 60)
    print("Testing the specific issue from the auto-tester")
    
    success = test_error_handling_fix()
    
    print(f"\n🎯 RESULT:")
    if success:
        print("🎉 SUCCESS: Error handling fix is working!")
        print("✅ No internal Python details exposed")
        print("✅ Ready to re-run auto-tester")
    else:
        print("❌ ISSUE: Fix not working yet")
        print("🔧 Action needed:")
        print("1. Update tool in OpenWebUI with the fixed version")
        print("2. Restart OpenWebUI if needed")
        print("3. Re-run this test")

if __name__ == "__main__":
    main()
