#!/usr/bin/env python3
"""
Test script to verify 16K context optimization is working correctly
"""

import requests
import json
import time

def test_chunk_counts():
    """Test that the new chunk count settings are working"""
    
    print("🧪 Testing 16K Context Optimization")
    print("=" * 60)
    
    # Test different query types and their expected chunk counts
    test_queries = [
        {
            "query": "find all error handling functions",
            "expected_chunks": 6,  # was 3
            "type": "direct search"
        },
        {
            "query": "how does memory management work in this codebase",
            "expected_chunks": 12,  # was 6
            "type": "explanation"
        },
        {
            "query": "compare the error handling between different modules",
            "expected_chunks": 15,  # was 8
            "type": "comparison"
        },
        {
            "query": "list all functions in the codebase",
            "expected_chunks": 20,  # was 10
            "type": "comprehensive listing"
        },
        {
            "query": "analyze the architecture of this system",
            "expected_chunks": 16,  # new category
            "type": "architecture analysis"
        },
        {
            "query": "what are the main components",
            "expected_chunks": 10,  # was 5
            "type": "default"
        }
    ]
    
    print("📋 Testing Query-Based Chunk Selection:")
    print()
    
    for i, test in enumerate(test_queries, 1):
        print(f"{i}. {test['type'].upper()}")
        print(f"   Query: '{test['query']}'")
        print(f"   Expected chunks: {test['expected_chunks']}")
        
        # Test with OpenWebUI tool API
        try:
            response = requests.post(
                "http://home-ai-server.local:8080/api/chat/completions",
                headers={
                    "Authorization": "Bearer sk-320242e0335e45a4b1fa4752f758f9ab",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "llama3:latest",
                    "messages": [
                        {
                            "role": "user", 
                            "content": f"select codebase utils then {test['query']}"
                        }
                    ],
                    "tool_ids": ["codebase_analyzer"],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                # Look for chunk count in the response
                response_text = str(result)
                if "chunks" in response_text.lower():
                    print(f"   ✅ Tool executed successfully")
                else:
                    print(f"   ⚠️ Tool executed but no chunk info found")
            else:
                print(f"   ❌ Tool execution failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error testing tool: {e}")
        
        print()

def test_server_limits():
    """Test that server-side limits have been updated"""
    
    print("🔧 Testing Server-Side Configuration:")
    print()
    
    # Test enhanced search endpoint
    try:
        response = requests.post(
            "http://home-ai-server.local:5002/tools/search_enhanced",
            json={
                "query": "test query",
                "codebase_name": "utils",
                "n_results": 25  # Test new maximum
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            chunks_returned = len(result.get('chunks', []))
            print(f"✅ Enhanced search accepts n_results=25")
            print(f"   Chunks returned: {chunks_returned}")
        else:
            print(f"❌ Enhanced search failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing enhanced search: {e}")
    
    # Test optimized context endpoint
    try:
        response = requests.post(
            "http://home-ai-server.local:5002/tools/get_optimized_context",
            json={
                "query": "test query for context",
                "codebase_name": "utils",
                "n_results": 20  # Test higher default
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            context = result.get('context', '')
            print(f"✅ Optimized context accepts n_results=20")
            print(f"   Context length: {len(context)} characters")
        else:
            print(f"❌ Optimized context failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing optimized context: {e}")

def test_context_quality():
    """Test that we're getting better context with more chunks"""
    
    print("\n📊 Testing Context Quality Improvement:")
    print()
    
    # Compare old vs new chunk counts for the same query
    test_query = "explain how error handling works in this codebase"
    
    print(f"Query: '{test_query}'")
    print()
    
    # Test with old chunk count (5)
    try:
        response_old = requests.post(
            "http://home-ai-server.local:5002/tools/get_optimized_context",
            json={
                "query": test_query,
                "codebase_name": "utils",
                "n_results": 5  # Old default
            },
            timeout=10
        )
        
        if response_old.status_code == 200:
            context_old = response_old.json().get('context', '')
            print(f"📉 Old context (5 chunks): {len(context_old)} characters")
        else:
            print(f"❌ Old context test failed")
            context_old = ""
            
    except Exception as e:
        print(f"❌ Error testing old context: {e}")
        context_old = ""
    
    # Test with new chunk count (12 for explanations)
    try:
        response_new = requests.post(
            "http://home-ai-server.local:5002/tools/get_optimized_context",
            json={
                "query": test_query,
                "codebase_name": "utils",
                "n_results": 12  # New default for explanations
            },
            timeout=10
        )
        
        if response_new.status_code == 200:
            context_new = response_new.json().get('context', '')
            print(f"📈 New context (12 chunks): {len(context_new)} characters")
            
            if len(context_new) > len(context_old):
                improvement = ((len(context_new) - len(context_old)) / len(context_old)) * 100
                print(f"✅ Context improvement: +{improvement:.1f}% more content")
            else:
                print(f"⚠️ Context not significantly improved")
        else:
            print(f"❌ New context test failed")
            
    except Exception as e:
        print(f"❌ Error testing new context: {e}")

def main():
    print("🎯 16K Context Optimization Test Suite")
    print("=" * 70)
    print("Testing the optimizations for 16384 token context length")
    print()
    
    # Test 1: Query-based chunk selection
    test_chunk_counts()
    
    # Test 2: Server-side limits
    test_server_limits()
    
    # Test 3: Context quality improvement
    test_context_quality()
    
    print("\n" + "=" * 70)
    print("📊 OPTIMIZATION SUMMARY:")
    print("=" * 70)
    
    print("✅ CHANGES APPLIED:")
    print("  - Maximum chunks: 10 → 20 (100% increase)")
    print("  - Default chunks: 5 → 10 (100% increase)")
    print("  - Comparison queries: 8 → 15 chunks (87% increase)")
    print("  - Explanation queries: 6 → 12 chunks (100% increase)")
    print("  - Direct search: 3 → 6 chunks (100% increase)")
    print("  - Comprehensive lists: 10 → 20 chunks (100% increase)")
    print("  - Added architecture analysis: 16 chunks (new)")
    print("  - Added deep analysis: 18 chunks (new)")
    
    print("\n🎯 EXPECTED BENEFITS:")
    print("  - 2-4x more code context per query")
    print("  - Better understanding of complex codebases")
    print("  - More comprehensive analysis and comparisons")
    print("  - Reduced need for follow-up questions")
    print("  - Better architectural insights")
    
    print("\n💡 USAGE RECOMMENDATIONS:")
    print("  - Use specific query types for optimal chunk selection")
    print("  - Try 'analyze', 'compare', 'explain' keywords")
    print("  - Ask for 'architecture' or 'design' for broad context")
    print("  - Use 'list all' for comprehensive overviews")
    
    print(f"\n🚀 Your OpenWebUI is now optimized for 16K context!")

if __name__ == "__main__":
    main()
