{".class": "MypyFile", "_fullname": "vector_db_creator", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EnhancedVectorDBCreator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "vector_db_creator.EnhancedVectorDBCreator", "name": "EnhancedVectorDBCreator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "vector_db_creator.EnhancedVectorDBCreator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "vector_db_creator", "mro": ["vector_db_creator.EnhancedVectorDBCreator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "db_path", "ollama_host", "use_ollama"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "vector_db_creator.EnhancedVectorDBCreator.__init__", "name": "__init__", "type": null}}, "_flatten_enhanced_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "vector_db_creator.EnhancedVectorDBCreator._flatten_enhanced_metadata", "name": "_flatten_enhanced_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "arg_types": ["vector_db_creator.EnhancedVectorDBCreator", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flatten_enhanced_metadata of EnhancedVectorDBCreator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_enhanced_document_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "vector_db_creator.EnhancedVectorDBCreator._format_enhanced_document_content", "name": "_format_enhanced_document_content", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": ["vector_db_creator.EnhancedVectorDBCreator", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_enhanced_document_content of EnhancedVectorDBCreator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_show_enhanced_chunk_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "vector_db_creator.EnhancedVectorDBCreator._show_enhanced_chunk_statistics", "name": "_show_enhanced_chunk_statistics", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "arg_types": ["vector_db_creator.EnhancedVectorDBCreator", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_show_enhanced_chunk_statistics of EnhancedVectorDBCreator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_enhanced_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "collection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "vector_db_creator.EnhancedVectorDBCreator._validate_enhanced_metadata", "name": "_validate_enhanced_metadata", "type": null}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.EnhancedVectorDBCreator.client", "name": "client", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "collection_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.EnhancedVectorDBCreator.collection_name", "name": "collection_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "chunks", "collection_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "vector_db_creator.EnhancedVectorDBCreator.create_collection", "name": "create_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "chunks", "collection_name"], "arg_types": ["vector_db_creator.EnhancedVectorDBCreator", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_collection of EnhancedVectorDBCreator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embedding_function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.EnhancedVectorDBCreator.embedding_function", "name": "embedding_function", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ollama_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.EnhancedVectorDBCreator.ollama_host", "name": "ollama_host", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "use_ollama": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.EnhancedVectorDBCreator.use_ollama", "name": "use_ollama", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "vector_db_creator.EnhancedVectorDBCreator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "vector_db_creator.EnhancedVectorDBCreator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "OllamaEmbeddingFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "vector_db_creator.OllamaEmbeddingFunction", "name": "OllamaEmbeddingFunction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "vector_db_creator.OllamaEmbeddingFunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "vector_db_creator", "mro": ["vector_db_creator.OllamaEmbeddingFunction", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "vector_db_creator.OllamaEmbeddingFunction.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input"], "arg_types": ["vector_db_creator.OllamaEmbeddingFunction", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of OllamaEmbeddingFunction", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "ollama_host", "model_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "vector_db_creator.OllamaEmbeddingFunction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "ollama_host", "model_name"], "arg_types": ["vector_db_creator.OllamaEmbeddingFunction", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OllamaEmbeddingFunction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.OllamaEmbeddingFunction.client", "name": "client", "type": "ollama._client.Client"}}, "embedding_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.OllamaEmbeddingFunction.embedding_dim", "name": "embedding_dim", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "model_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.OllamaEmbeddingFunction.model_name", "name": "model_name", "type": "builtins.str"}}, "ollama_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.OllamaEmbeddingFunction.ollama_host", "name": "ollama_host", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "vector_db_creator.OllamaEmbeddingFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "vector_db_creator.OllamaEmbeddingFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "VectorDBCreator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "vector_db_creator.VectorDBCreator", "line": 684, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "vector_db_creator.EnhancedVectorDBCreator"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "vector_db_creator.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "vector_db_creator.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "vector_db_creator.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "vector_db_creator.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "vector_db_creator.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "vector_db_creator.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "chromadb": {".class": "SymbolTableNode", "cross_ref": "chromadb", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "vector_db_creator.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "embedding_functions": {".class": "SymbolTableNode", "cross_ref": "chromadb.utils.embedding_functions", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "ollama": {".class": "SymbolTableNode", "cross_ref": "ollama", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\vector_db_creator.py"}