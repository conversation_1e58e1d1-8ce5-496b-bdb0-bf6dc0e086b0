#!/usr/bin/env python3
"""
Test script to verify new language support in the code analyzer.
Tests TypeScript, JavaScript, Rust, and Java language detection and processing.
"""

import os
import tempfile
from pathlib import Path
from code_preprocessor import MultiLanguageCodeProcessor

def create_test_files():
    """Create sample files for each new language"""
    test_files = {}
    
    # JavaScript test file
    js_content = """
// JavaScript example with async operations
async function fetchData(url) {
    try {
        const response = await fetch(url);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching data:', error);
        throw error;
    }
}

// React component example
function UserProfile({ user }) {
    return (
        <div className="user-profile">
            <h1>{user.name}</h1>
            <p>{user.email}</p>
        </div>
    );
}

module.exports = { fetchData, UserProfile };
"""
    
    # TypeScript test file
    ts_content = """
// TypeScript example with interfaces and generics
interface User {
    id: number;
    name: string;
    email: string;
}

interface ApiResponse<T> {
    data: T;
    status: number;
    message: string;
}

class UserService {
    async getUser(id: number): Promise<ApiResponse<User>> {
        try {
            const response = await fetch(`/api/users/${id}`);
            const result: ApiResponse<User> = await response.json();
            return result;
        } catch (error) {
            throw new Error(`Failed to fetch user: ${error.message}`);
        }
    }
}

export { User, ApiResponse, UserService };
"""
    
    # Rust test file
    rust_content = """
// Rust example with ownership and error handling
use std::collections::HashMap;
use std::fs::File;
use std::io::{self, Read};

#[derive(Debug, Clone)]
pub struct User {
    pub id: u32,
    pub name: String,
    pub email: String,
}

impl User {
    pub fn new(id: u32, name: String, email: String) -> Self {
        User { id, name, email }
    }
}

pub fn read_config_file(path: &str) -> Result<String, io::Error> {
    let mut file = File::open(path)?;
    let mut contents = String::new();
    file.read_to_string(&mut contents)?;
    Ok(contents)
}

pub async fn process_users(users: Vec<User>) -> Result<HashMap<u32, User>, Box<dyn std::error::Error>> {
    let mut user_map = HashMap::new();
    
    for user in users {
        user_map.insert(user.id, user);
    }
    
    Ok(user_map)
}
"""
    
    # Java test file
    java_content = """
// Java example with Spring framework and concurrency
package com.example.service;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.List;
import java.util.ArrayList;

@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    private final ExecutorService executor = Executors.newFixedThreadPool(10);
    
    public CompletableFuture<List<User>> getAllUsersAsync() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return userRepository.findAll();
            } catch (Exception e) {
                throw new RuntimeException("Failed to fetch users", e);
            }
        }, executor);
    }
    
    public User createUser(String name, String email) throws ValidationException {
        if (name == null || name.trim().isEmpty()) {
            throw new ValidationException("Name cannot be empty");
        }
        
        User user = new User();
        user.setName(name);
        user.setEmail(email);
        
        return userRepository.save(user);
    }
}
"""
    
    # Create temporary files
    temp_dir = tempfile.mkdtemp()
    
    test_files['javascript'] = Path(temp_dir) / 'test.js'
    test_files['javascript'].write_text(js_content)
    
    test_files['typescript'] = Path(temp_dir) / 'test.ts'
    test_files['typescript'].write_text(ts_content)
    
    test_files['rust'] = Path(temp_dir) / 'test.rs'
    test_files['rust'].write_text(rust_content)
    
    test_files['java'] = Path(temp_dir) / 'Test.java'
    test_files['java'].write_text(java_content)
    
    return test_files, temp_dir

def test_language_detection():
    """Test that the processor correctly identifies file languages"""
    print("🧪 Testing language detection...")
    
    test_files, temp_dir = create_test_files()
    processor = MultiLanguageCodeProcessor(temp_dir)
    
    for lang, filepath in test_files.items():
        parser, language, lang_name = processor.get_parser_for_file(filepath)
        print(f"  {filepath.suffix}: {lang_name} (parser: {'✓' if parser else '✗'})")
        
        if lang_name != lang:
            print(f"    ⚠️  Expected {lang}, got {lang_name}")
        else:
            print(f"    ✅ Language detection correct")
    
    return test_files, temp_dir

def test_semantic_patterns():
    """Test that semantic patterns are available for new languages"""
    print("\n🔍 Testing semantic patterns...")
    
    processor = MultiLanguageCodeProcessor(".")
    patterns = processor.semantic_patterns
    
    new_languages = ['javascript', 'typescript', 'rust', 'java']
    
    for lang in new_languages:
        if lang in patterns:
            print(f"  ✅ {lang.capitalize()}: {len(patterns[lang])} pattern categories")
            for category, keywords in patterns[lang].items():
                print(f"    - {category}: {len(keywords)} keywords")
        else:
            print(f"  ❌ {lang.capitalize()}: No patterns found")

def test_file_processing():
    """Test that files can be processed without errors"""
    print("\n⚙️  Testing file processing...")
    
    test_files, temp_dir = create_test_files()
    processor = MultiLanguageCodeProcessor(temp_dir)
    
    for lang, filepath in test_files.items():
        try:
            content = filepath.read_text()
            parser, language, lang_name = processor.get_parser_for_file(filepath)
            
            if parser:
                # Try to parse the file
                tree = parser.parse(bytes(content, 'utf8'))
                print(f"  ✅ {lang.capitalize()}: Parsed successfully ({len(content)} chars)")
            else:
                print(f"  ⚠️  {lang.capitalize()}: No parser available, using text processing")
                
        except Exception as e:
            print(f"  ❌ {lang.capitalize()}: Error - {e}")
    
    # Cleanup
    import shutil
    shutil.rmtree(temp_dir)

def main():
    """Run all tests"""
    print("🚀 Testing new language support for Code Analyzer")
    print("=" * 60)
    
    try:
        test_language_detection()
        test_semantic_patterns()
        test_file_processing()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
