{".class": "MypyFile", "_fullname": "lines_of_code_counter", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CodebaseStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lines_of_code_counter.CodebaseStats", "name": "CodebaseStats", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.CodebaseStats", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "codebase_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "total_files", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "total_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "total_code_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "total_comment_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "total_blank_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "total_size_bytes", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "languages", "type": {".class": "Instance", "args": ["builtins.str", "lines_of_code_counter.LanguageStats"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "analysis_date", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "largest_files", "type": {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "most_complex_files", "type": {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "lines_of_code_counter", "mro": ["lines_of_code_counter.CodebaseStats", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "codebase_name", "total_files", "total_lines", "total_code_lines", "total_comment_lines", "total_blank_lines", "total_size_bytes", "languages", "analysis_date", "largest_files", "most_complex_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.CodebaseStats.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "codebase_name", "total_files", "total_lines", "total_code_lines", "total_comment_lines", "total_blank_lines", "total_size_bytes", "languages", "analysis_date", "largest_files", "most_complex_files"], "arg_types": ["lines_of_code_counter.CodebaseStats", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.str", "lines_of_code_counter.LanguageStats"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CodebaseStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "lines_of_code_counter.CodebaseStats.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "codebase_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_files"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_code_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_comment_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_blank_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_size_bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "languages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "analysis_date"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "largest_files"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "most_complex_files"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["codebase_name", "total_files", "total_lines", "total_code_lines", "total_comment_lines", "total_blank_lines", "total_size_bytes", "languages", "analysis_date", "largest_files", "most_complex_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "lines_of_code_counter.CodebaseStats.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["codebase_name", "total_files", "total_lines", "total_code_lines", "total_comment_lines", "total_blank_lines", "total_size_bytes", "languages", "analysis_date", "largest_files", "most_complex_files"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.str", "lines_of_code_counter.LanguageStats"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CodebaseStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["codebase_name", "total_files", "total_lines", "total_code_lines", "total_comment_lines", "total_blank_lines", "total_size_bytes", "languages", "analysis_date", "largest_files", "most_complex_files"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.str", "lines_of_code_counter.LanguageStats"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CodebaseStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "analysis_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.analysis_date", "name": "analysis_date", "type": "builtins.str"}}, "codebase_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.codebase_name", "name": "codebase_name", "type": "builtins.str"}}, "languages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.languages", "name": "languages", "type": {".class": "Instance", "args": ["builtins.str", "lines_of_code_counter.LanguageStats"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "largest_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.largest_files", "name": "largest_files", "type": {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "most_complex_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.most_complex_files", "name": "most_complex_files", "type": {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "total_blank_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.total_blank_lines", "name": "total_blank_lines", "type": "builtins.int"}}, "total_code_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.total_code_lines", "name": "total_code_lines", "type": "builtins.int"}}, "total_comment_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.total_comment_lines", "name": "total_comment_lines", "type": "builtins.int"}}, "total_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.total_files", "name": "total_files", "type": "builtins.int"}}, "total_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.total_lines", "name": "total_lines", "type": "builtins.int"}}, "total_size_bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.CodebaseStats.total_size_bytes", "name": "total_size_bytes", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lines_of_code_counter.CodebaseStats.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lines_of_code_counter.CodebaseStats", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FileStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lines_of_code_counter.FileStats", "name": "FileStats", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.FileStats", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 18, "name": "filepath", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 19, "name": "relative_path", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 20, "name": "language", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 21, "name": "total_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 22, "name": "code_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 23, "name": "comment_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 24, "name": "blank_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 25, "name": "file_size_bytes", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 26, "name": "last_modified", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "lines_of_code_counter", "mro": ["lines_of_code_counter.FileStats", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "lines_of_code_counter.FileStats.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "filepath", "relative_path", "language", "total_lines", "code_lines", "comment_lines", "blank_lines", "file_size_bytes", "last_modified"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.FileStats.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "filepath", "relative_path", "language", "total_lines", "code_lines", "comment_lines", "blank_lines", "file_size_bytes", "last_modified"], "arg_types": ["lines_of_code_counter.FileStats", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "lines_of_code_counter.FileStats.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "filepath"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "relative_path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "language"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "code_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "comment_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blank_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "file_size_bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last_modified"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["filepath", "relative_path", "language", "total_lines", "code_lines", "comment_lines", "blank_lines", "file_size_bytes", "last_modified"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "lines_of_code_counter.FileStats.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["filepath", "relative_path", "language", "total_lines", "code_lines", "comment_lines", "blank_lines", "file_size_bytes", "last_modified"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FileStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "lines_of_code_counter.FileStats.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["filepath", "relative_path", "language", "total_lines", "code_lines", "comment_lines", "blank_lines", "file_size_bytes", "last_modified"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FileStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "blank_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.blank_lines", "name": "blank_lines", "type": "builtins.int"}}, "code_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.code_lines", "name": "code_lines", "type": "builtins.int"}}, "comment_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.comment_lines", "name": "comment_lines", "type": "builtins.int"}}, "file_size_bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.file_size_bytes", "name": "file_size_bytes", "type": "builtins.int"}}, "filepath": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.filepath", "name": "filepath", "type": "builtins.str"}}, "language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.language", "name": "language", "type": "builtins.str"}}, "last_modified": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.last_modified", "name": "last_modified", "type": "builtins.str"}}, "relative_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.relative_path", "name": "relative_path", "type": "builtins.str"}}, "total_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.FileStats.total_lines", "name": "total_lines", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lines_of_code_counter.FileStats.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lines_of_code_counter.FileStats", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LanguageStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lines_of_code_counter.LanguageStats", "name": "LanguageStats", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LanguageStats", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "language", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "file_count", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "total_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "code_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 36, "name": "comment_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 37, "name": "blank_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "total_size_bytes", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "files", "type": {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "lines_of_code_counter", "mro": ["lines_of_code_counter.LanguageStats", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "language", "file_count", "total_lines", "code_lines", "comment_lines", "blank_lines", "total_size_bytes", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LanguageStats.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "language", "file_count", "total_lines", "code_lines", "comment_lines", "blank_lines", "total_size_bytes", "files"], "arg_types": ["lines_of_code_counter.LanguageStats", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LanguageStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "lines_of_code_counter.LanguageStats.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "language"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "file_count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "code_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "comment_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blank_lines"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_size_bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "files"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["language", "file_count", "total_lines", "code_lines", "comment_lines", "blank_lines", "total_size_bytes", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "lines_of_code_counter.LanguageStats.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["language", "file_count", "total_lines", "code_lines", "comment_lines", "blank_lines", "total_size_bytes", "files"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LanguageStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["language", "file_count", "total_lines", "code_lines", "comment_lines", "blank_lines", "total_size_bytes", "files"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of LanguageStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "blank_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.blank_lines", "name": "blank_lines", "type": "builtins.int"}}, "code_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.code_lines", "name": "code_lines", "type": "builtins.int"}}, "comment_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.comment_lines", "name": "comment_lines", "type": "builtins.int"}}, "file_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.file_count", "name": "file_count", "type": "builtins.int"}}, "files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.files", "name": "files", "type": {".class": "Instance", "args": ["lines_of_code_counter.FileStats"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "language": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.language", "name": "language", "type": "builtins.str"}}, "total_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.total_lines", "name": "total_lines", "type": "builtins.int"}}, "total_size_bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lines_of_code_counter.LanguageStats.total_size_bytes", "name": "total_size_bytes", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lines_of_code_counter.LanguageStats.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lines_of_code_counter.LanguageStats", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LinesOfCodeCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lines_of_code_counter.LinesOfCodeCounter", "name": "LinesOfCodeCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "lines_of_code_counter", "mro": ["lines_of_code_counter.LinesOfCodeCounter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source_code_base_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source_code_base_path"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LinesOfCodeCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bytes_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter._format_bytes", "name": "_format_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bytes_count"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_bytes of LinesOfCodeCounter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_percentage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "part", "total"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter._percentage", "name": "_percentage", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "part", "total"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_percentage of LinesOfCodeCounter", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "analyze_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "codebase_name", "exclude_dirs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter.analyze_codebase", "name": "analyze_codebase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "codebase_name", "exclude_dirs"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_codebase of LinesOfCodeCounter", "ret_type": "lines_of_code_counter.CodebaseStats", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "comment_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "lines_of_code_counter.LinesOfCodeCounter.comment_patterns", "name": "comment_patterns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "count_lines_in_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter.count_lines_in_file", "name": "count_lines_in_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count_lines_in_file of LinesOfCodeCounter", "ret_type": "lines_of_code_counter.FileStats", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_stats_for_display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter.format_stats_for_display", "name": "format_stats_for_display", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stats"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "lines_of_code_counter.CodebaseStats"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_stats_for_display of LinesOfCodeCounter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_language_from_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter.get_language_from_extension", "name": "get_language_from_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_language_from_extension of LinesOfCodeCounter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stats_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter.get_stats_summary", "name": "get_stats_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stats"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "lines_of_code_counter.CodebaseStats"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stats_summary of LinesOfCodeCounter", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_source_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lines_of_code_counter.LinesOfCodeCounter.is_source_file", "name": "is_source_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "arg_types": ["lines_of_code_counter.LinesOfCodeCounter", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_source_file of LinesOfCodeCounter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "language_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "lines_of_code_counter.LinesOfCodeCounter.language_extensions", "name": "language_extensions", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "source_code_base_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "lines_of_code_counter.LinesOfCodeCounter.source_code_base_path", "name": "source_code_base_path", "type": "pathlib.Path"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lines_of_code_counter.LinesOfCodeCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lines_of_code_counter.LinesOfCodeCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lines_of_code_counter.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lines_of_code_counter.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lines_of_code_counter.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lines_of_code_counter.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lines_of_code_counter.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lines_of_code_counter.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "counter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "lines_of_code_counter.counter", "name": "counter", "type": "lines_of_code_counter.LinesOfCodeCounter"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "lines_of_code_counter.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "stats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "lines_of_code_counter.stats", "name": "stats", "type": "lines_of_code_counter.CodebaseStats"}}, "summary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "lines_of_code_counter.summary", "name": "summary", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\lines_of_code_counter.py"}