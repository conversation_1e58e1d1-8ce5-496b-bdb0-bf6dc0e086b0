#!/usr/bin/env python3
"""
Comprehensive audit of all 27 supported languages to identify potential processing issues
"""

import tempfile
import shutil
from pathlib import Path
from code_preprocessor import MultiLanguageCodeProcessor

def create_sample_files():
    """Create sample files for all 27 supported languages"""
    
    samples = {
        # Core languages
        'main.c': '''
#include <stdio.h>
#include <stdlib.h>

int main() {
    printf("Hello, C!\\n");
    return 0;
}
''',
        'main.cpp': '''
#include <iostream>
#include <vector>
using namespace std;

class HelloWorld {
public:
    void greet() {
        cout << "Hello, C++!" << endl;
    }
};

int main() {
    HelloWorld hw;
    hw.greet();
    return 0;
}
''',
        'main.py': '''
#!/usr/bin/env python3
"""Main Python module"""

import sys
import os
from typing import List, Dict

class HelloWorld:
    def __init__(self, name: str):
        self.name = name
    
    def greet(self) -> str:
        return f"Hello, {self.name}!"

def main():
    hw = HelloWorld("Python")
    print(hw.greet())

if __name__ == "__main__":
    main()
''',
        'Program.cs': '''
using System;
using System.Collections.Generic;
using System.Linq;

namespace HelloWorld
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Hello, C#!");
        }
    }
    
    public class Person
    {
        public string Name { get; set; }
        public int Age { get; set; }
    }
}
''',
        'main.js': '''
// JavaScript main file
const express = require('express');
const path = require('path');

class HelloWorld {
    constructor(name) {
        this.name = name;
    }
    
    greet() {
        return `Hello, ${this.name}!`;
    }
}

function main() {
    const hw = new HelloWorld('JavaScript');
    console.log(hw.greet());
}

module.exports = { HelloWorld };
main();
''',
        'main.ts': '''
// TypeScript main file
import { EventEmitter } from 'events';

interface Greeter {
    name: string;
    greet(): string;
}

class HelloWorld implements Greeter {
    constructor(public name: string) {}
    
    greet(): string {
        return `Hello, ${this.name}!`;
    }
}

function main(): void {
    const hw = new HelloWorld('TypeScript');
    console.log(hw.greet());
}

export { HelloWorld, Greeter };
main();
''',
        'main.rs': '''
//! Main Rust module
use std::collections::HashMap;
use std::fmt::Display;

#[derive(Debug, Clone)]
pub struct Person {
    pub name: String,
    pub age: u32,
}

impl Display for Person {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "{} ({})", self.name, self.age)
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let person = Person {
        name: "Rust".to_string(),
        age: 15,
    };
    println!("Hello, {}!", person);
    Ok(())
}
''',
        'Main.java': '''
package com.example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Main Java class
 */
public class Main {
    private String name;
    
    public Main(String name) {
        this.name = name;
    }
    
    public String greet() {
        return "Hello, " + name + "!";
    }
    
    public static void main(String[] args) {
        Main main = new Main("Java");
        System.out.println(main.greet());
    }
}
''',
        'main.go': '''
package main

import (
    "fmt"
    "log"
    "net/http"
)

type Person struct {
    Name string `json:"name"`
    Age  int    `json:"age"`
}

func (p Person) Greet() string {
    return fmt.Sprintf("Hello, %s!", p.Name)
}

func main() {
    person := Person{Name: "Go", Age: 15}
    fmt.Println(person.Greet())
}
''',
        'schema.sql': '''
-- SQL schema file
CREATE DATABASE IF NOT EXISTS test_db;
USE test_db;

CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users (name, email) VALUES 
    ('John Doe', '<EMAIL>'),
    ('Jane Smith', '<EMAIL>');

SELECT * FROM users WHERE created_at > '2024-01-01';
''',
        'main.tcl': '''
#!/usr/bin/env tclsh
# TCL script

package require Tcl 8.5

proc greet {name} {
    return "Hello, $name!"
}

set name "TCL"
puts [greet $name]

# List operations
set numbers {1 2 3 4 5}
foreach num $numbers {
    puts "Number: $num"
}
''',
        'counter.v': '''
// Verilog counter module
module counter (
    input wire clk,
    input wire reset,
    input wire enable,
    output reg [7:0] count
);

always @(posedge clk or posedge reset) begin
    if (reset) begin
        count <= 8'b0;
    end else if (enable) begin
        count <= count + 1;
    end
end

endmodule
''',
        'script.sh': '''
#!/bin/bash
# Bash script

set -euo pipefail

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly LOG_FILE="/tmp/script.log"

function log_info() {
    echo "[INFO] $*" | tee -a "$LOG_FILE"
}

function main() {
    log_info "Starting script..."
    
    local name="Bash"
    echo "Hello, $name!"
    
    # Array operations
    local -a languages=("C" "Python" "Rust" "Go")
    for lang in "${languages[@]}"; do
        log_info "Language: $lang"
    done
}

main "$@"
''',
        'hello.lisp': '''
;;; Common Lisp file
(defpackage :hello-world
  (:use :cl)
  (:export #:greet #:main))

(in-package :hello-world)

(defclass person ()
  ((name :initarg :name :accessor person-name)
   (age :initarg :age :accessor person-age)))

(defmethod greet ((p person))
  (format nil "Hello, ~A!" (person-name p)))

(defun main ()
  (let ((person (make-instance 'person :name "Common Lisp" :age 40)))
    (format t "~A~%" (greet person))))
''',
        'hello.el': '''
;;; Emacs Lisp file
;;; Commentary:
;;; Simple Emacs Lisp example

;;; Code:

(defvar hello-world-name "Emacs Lisp"
  "Name for greeting.")

(defun hello-world-greet (name)
  "Greet NAME with a hello message."
  (format "Hello, %s!" name))

(defun hello-world-main ()
  "Main function."
  (interactive)
  (message "%s" (hello-world-greet hello-world-name)))

(provide 'hello)
;;; hello.el ends here
''',
        'hello.scm': '''
;;; Scheme file
(define-module (hello-world)
  #:export (greet main))

(define (greet name)
  "Greet someone by name"
  (string-append "Hello, " name "!"))

(define (main)
  "Main function"
  (display (greet "Scheme"))
  (newline))

;; List operations
(define numbers '(1 2 3 4 5))
(define squared (map (lambda (x) (* x x)) numbers))
''',
        'hello.lua': '''
-- Lua script
local HelloWorld = {}
HelloWorld.__index = HelloWorld

function HelloWorld:new(name)
    local obj = {
        name = name or "Lua"
    }
    setmetatable(obj, self)
    return obj
end

function HelloWorld:greet()
    return "Hello, " .. self.name .. "!"
end

-- Module table operations
local languages = {"C", "Python", "Rust", "Lua"}
for i, lang in ipairs(languages) do
    print(i .. ": " .. lang)
end

local hw = HelloWorld:new("Lua")
print(hw:greet())

return HelloWorld
''',
        'Makefile': '''
# Makefile
CC = gcc
CFLAGS = -Wall -Wextra -std=c99
TARGET = hello
SOURCES = main.c utils.c
OBJECTS = $(SOURCES:.c=.o)

.PHONY: all clean install

all: $(TARGET)

$(TARGET): $(OBJECTS)
	$(CC) $(CFLAGS) -o $@ $^

%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	rm -f $(OBJECTS) $(TARGET)

install: $(TARGET)
	cp $(TARGET) /usr/local/bin/
''',
        'config.json': '''
{
  "name": "test-project",
  "version": "1.0.0",
  "description": "Test configuration",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "test": "jest",
    "build": "webpack --mode production"
  },
  "dependencies": {
    "express": "^4.18.0",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "jest": "^29.0.0",
    "webpack": "^5.74.0"
  },
  "keywords": ["test", "example", "json"],
  "author": "Test Author",
  "license": "MIT"
}
''',
        'config.yaml': '''
# YAML configuration
name: test-project
version: 1.0.0
description: Test YAML configuration

database:
  host: localhost
  port: 5432
  name: testdb
  credentials:
    username: admin
    password: secret

services:
  - name: web
    port: 8080
    replicas: 3
    environment:
      NODE_ENV: production
      DEBUG: false
  - name: api
    port: 3000
    replicas: 2
    environment:
      NODE_ENV: production
      API_KEY: ${API_KEY}

logging:
  level: info
  format: json
  outputs:
    - console
    - file: /var/log/app.log
''',
        'data.xml': '''
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://example.com/schema"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://example.com/schema project.xsd">
    
    <metadata>
        <name>Test Project</name>
        <version>1.0.0</version>
        <description>XML configuration example</description>
    </metadata>
    
    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>utils</artifactId>
            <version>2.1.0</version>
        </dependency>
    </dependencies>
    
    <configuration>
        <properties>
            <property name="debug" value="true"/>
            <property name="timeout" value="30"/>
        </properties>
    </configuration>
    
</project>
''',
        'index.php': '''
<?php
/**
 * PHP example file
 */

namespace HelloWorld;

use Exception;
use PDO;

class Greeter {
    private string $name;
    
    public function __construct(string $name) {
        $this->name = $name;
    }
    
    public function greet(): string {
        return "Hello, {$this->name}!";
    }
}

interface DatabaseInterface {
    public function connect(): PDO;
}

function main(): void {
    try {
        $greeter = new Greeter('PHP');
        echo $greeter->greet() . PHP_EOL;
        
        $languages = ['PHP', 'Python', 'JavaScript'];
        foreach ($languages as $lang) {
            echo "Language: $lang" . PHP_EOL;
        }
    } catch (Exception $e) {
        error_log("Error: " . $e->getMessage());
    }
}

main();
?>
''',
        'script.pl': '''
#!/usr/bin/perl
# Perl script

use strict;
use warnings;
use feature 'say';

package HelloWorld;

sub new {
    my ($class, $name) = @_;
    my $self = {
        name => $name // 'Perl'
    };
    bless $self, $class;
    return $self;
}

sub greet {
    my $self = shift;
    return "Hello, " . $self->{name} . "!";
}

package main;

my $hw = HelloWorld->new('Perl');
say $hw->greet();

# Array operations
my @languages = qw(Perl Python Ruby);
for my $lang (@languages) {
    say "Language: $lang";
}

# Hash operations
my %config = (
    debug => 1,
    timeout => 30,
    host => 'localhost'
);

while (my ($key, $value) = each %config) {
    say "$key: $value";
}
''',
        'README.md': '''
# Test Project

This is a **test project** for language processing.

## Features

- Multi-language support
- Code analysis
- Documentation generation

## Languages Supported

1. C/C++
2. Python
3. Rust
4. JavaScript/TypeScript
5. And many more...

### Code Example

```python
def hello_world():
    print("Hello, World!")
```

## Installation

```bash
git clone https://github.com/example/test-project.git
cd test-project
make install
```

> **Note**: This is just a test file.
''',
        'index.html': '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test HTML Page</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .container { max-width: 800px; margin: 0 auto; }
        .highlight { background-color: yellow; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hello, HTML!</h1>
        <p>This is a <span class="highlight">test</span> HTML file.</p>
        
        <ul>
            <li>Item 1</li>
            <li>Item 2</li>
            <li>Item 3</li>
        </ul>
        
        <script>
            console.log('Hello from embedded JavaScript!');
            
            function greet(name) {
                return `Hello, ${name}!`;
            }
            
            document.addEventListener('DOMContentLoaded', function() {
                console.log(greet('HTML'));
            });
        </script>
    </div>
</body>
</html>
''',
        'program.f90': '''
! Fortran 90 program
program hello_world
    implicit none
    
    ! Variable declarations
    character(len=20) :: name = 'Fortran'
    integer, parameter :: n = 5
    integer :: i
    real, dimension(n) :: numbers
    
    ! Initialize array
    do i = 1, n
        numbers(i) = real(i) * 2.0
    end do
    
    ! Main logic
    call greet(name)
    call print_numbers(numbers, n)
    
contains
    
    subroutine greet(person_name)
        character(len=*), intent(in) :: person_name
        write(*,*) 'Hello, ', trim(person_name), '!'
    end subroutine greet
    
    subroutine print_numbers(arr, size)
        integer, intent(in) :: size
        real, dimension(size), intent(in) :: arr
        integer :: j
        
        write(*,*) 'Numbers:'
        do j = 1, size
            write(*,*) j, ':', arr(j)
        end do
    end subroutine print_numbers
    
end program hello_world
''',
        'counter.vhd': '''
-- VHDL counter entity
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.NUMERIC_STD.ALL;

entity counter is
    Port ( 
        clk : in STD_LOGIC;
        reset : in STD_LOGIC;
        enable : in STD_LOGIC;
        count : out STD_LOGIC_VECTOR (7 downto 0)
    );
end counter;

architecture Behavioral of counter is
    signal count_int : unsigned(7 downto 0) := (others => '0');
begin
    
    process(clk, reset)
    begin
        if reset = '1' then
            count_int <= (others => '0');
        elsif rising_edge(clk) then
            if enable = '1' then
                count_int <= count_int + 1;
            end if;
        end if;
    end process;
    
    count <= std_logic_vector(count_int);
    
end Behavioral;
''',
        'Cargo.toml': '''
[package]
name = "test-project"
version = "0.1.0"
edition = "2021"
authors = ["Test Author <<EMAIL>>"]
description = "A test Rust project"
license = "MIT"
repository = "https://github.com/example/test-project"

[dependencies]
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = ["full"] }
clap = { version = "4.0", features = ["derive"] }

[dev-dependencies]
criterion = "0.5"

[[bin]]
name = "main"
path = "src/main.rs"
'''
    }
    
    return samples

def audit_language_processing():
    """Audit all languages for processing issues"""
    
    print("🔍 COMPREHENSIVE LANGUAGE PROCESSING AUDIT")
    print("=" * 80)
    
    # Create temporary directory with all sample files
    temp_dir = tempfile.mkdtemp()
    
    try:
        test_dir = Path(temp_dir) / "multi_language_test"
        test_dir.mkdir()
        
        # Create all sample files
        samples = create_sample_files()
        
        print(f"📁 Created test directory: {test_dir}")
        print(f"📄 Created {len(samples)} sample files:")
        
        for filename, content in samples.items():
            file_path = test_dir / filename
            file_path.write_text(content)
            print(f"  - {filename} ({len(content)} chars)")
        
        # Test processing
        print(f"\n🧪 Testing Multi-Language Processing...")
        processor = MultiLanguageCodeProcessor(str(test_dir))
        
        # Process all files
        chunks = processor.process_repository(str(test_dir))
        
        print(f"\n📊 PROCESSING RESULTS:")
        print(f"Total files created: {len(samples)}")
        print(f"Total chunks generated: {len(chunks)}")
        
        if len(chunks) == 0:
            print("❌ CRITICAL: No chunks generated at all!")
            return False
        
        # Analyze by language
        language_stats = {}
        extension_stats = {}
        
        for chunk in chunks:
            metadata = chunk.get('metadata', {})
            lang = metadata.get('language', 'unknown')
            file_path = metadata.get('filepath', '')
            
            if file_path:
                ext = Path(file_path).suffix.lower()
                extension_stats[ext] = extension_stats.get(ext, 0) + 1
            
            language_stats[lang] = language_stats.get(lang, 0) + 1
        
        print(f"\n📈 LANGUAGE DISTRIBUTION:")
        for lang, count in sorted(language_stats.items()):
            print(f"  {lang}: {count} chunks")
        
        print(f"\n📋 EXTENSION DISTRIBUTION:")
        for ext, count in sorted(extension_stats.items()):
            print(f"  {ext}: {count} chunks")
        
        # Check for potential issues
        issues = []
        
        # Check if any major languages have 0 chunks
        expected_languages = {
            '.c': 'c', '.cpp': 'cpp', '.py': 'python', '.cs': 'csharp',
            '.js': 'javascript', '.ts': 'typescript', '.rs': 'rust',
            '.java': 'java', '.go': 'go', '.sql': 'sql'
        }
        
        for ext, expected_lang in expected_languages.items():
            if ext not in extension_stats:
                issues.append(f"❌ {ext} files not processed (expected {expected_lang})")
            elif extension_stats[ext] == 0:
                issues.append(f"❌ {ext} files found but generated 0 chunks")
        
        # Check for unknown languages
        unknown_count = language_stats.get('unknown', 0)
        if unknown_count > 0:
            issues.append(f"⚠️ {unknown_count} chunks marked as 'unknown' language")
        
        print(f"\n🔍 ISSUE ANALYSIS:")
        if issues:
            for issue in issues:
                print(f"  {issue}")
        else:
            print("  ✅ No major issues detected")
        
        # Show sample chunks for verification
        print(f"\n📋 SAMPLE CHUNKS (first 5):")
        for i, chunk in enumerate(chunks[:5]):
            metadata = chunk.get('metadata', {})
            content_preview = chunk.get('content', '')[:100].replace('\n', ' ')
            print(f"  {i+1}. {metadata.get('relative_path', 'unknown')} "
                  f"({metadata.get('language', 'unknown')}) - {content_preview}...")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ Error during audit: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        shutil.rmtree(temp_dir)

if __name__ == "__main__":
    print("🔍 Multi-Language Processing Audit")
    print("=" * 80)
    
    success = audit_language_processing()
    
    print("\n" + "=" * 80)
    print("📊 AUDIT SUMMARY:")
    print("=" * 80)
    
    if success:
        print("🎉 SUCCESS: All languages appear to be processing correctly!")
        print("✅ No critical issues found in language processing")
    else:
        print("❌ ISSUES FOUND: Some languages may have processing problems")
        print("🔧 Review the issues above and apply fixes as needed")
    
    print("\n💡 RECOMMENDATIONS:")
    print("1. Run this audit after any changes to language processing")
    print("2. Add new sample files when supporting additional languages")
    print("3. Monitor chunk generation for all supported file types")
    print("4. Verify header detection works for each language's syntax")
