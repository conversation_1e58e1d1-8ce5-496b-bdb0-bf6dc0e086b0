#!/usr/bin/env python3
"""
Diagnose why the bookstore codebase isn't generating any code chunks
"""

import tempfile
import shutil
from pathlib import Path
from code_preprocessor import MultiLanguageCodeProcessor

def test_typescript_processing():
    """Test TypeScript processing with sample files"""
    
    print("🔍 Diagnosing Bookstore Processing Issue")
    print("=" * 60)
    
    # Create temporary directory with sample TypeScript files
    temp_dir = tempfile.mkdtemp()
    
    try:
        bookstore_dir = Path(temp_dir) / "bookstore"
        bookstore_dir.mkdir()
        
        # Create sample TypeScript files similar to what should be in bookstore
        
        # 1. Types file
        types_file = bookstore_dir / "types.ts"
        types_file.write_text("""
// TypeScript interfaces for bookstore application
export interface Book {
  id: string;
  title: string;
  author: string;
  isbn: string;
  price: number;
  category: BookCategory;
  publishedDate: Date;
  inStock: boolean;
}

export enum BookCategory {
  FICTION = 'fiction',
  NON_FICTION = 'non-fiction',
  SCIENCE = 'science',
  TECHNOLOGY = 'technology',
  HISTORY = 'history'
}

export interface Author {
  id: string;
  name: string;
  biography?: string;
  books: string[]; // Book IDs
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  address: Address;
  orders: Order[];
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}
""")
        
        # 2. Service file
        service_file = bookstore_dir / "BookService.ts"
        service_file.write_text("""
import { Book, BookCategory, Author } from './types';

export class BookService {
  private books: Map<string, Book> = new Map();
  private authors: Map<string, Author> = new Map();

  async createBook(bookData: Omit<Book, 'id'>): Promise<Book> {
    const id = this.generateId();
    const book: Book = {
      id,
      ...bookData
    };
    
    this.books.set(id, book);
    return book;
  }

  async getBookById(id: string): Promise<Book | null> {
    return this.books.get(id) || null;
  }

  async searchBooks(query: string): Promise<Book[]> {
    const results: Book[] = [];
    
    for (const book of this.books.values()) {
      if (
        book.title.toLowerCase().includes(query.toLowerCase()) ||
        book.author.toLowerCase().includes(query.toLowerCase()) ||
        book.isbn.includes(query)
      ) {
        results.push(book);
      }
    }
    
    return results;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
""")
        
        # 3. Package.json
        package_file = bookstore_dir / "package.json"
        package_file.write_text("""
{
  "name": "bookstore-app",
  "version": "1.0.0",
  "description": "TypeScript bookstore application",
  "main": "dist/index.js",
  "scripts": {
    "build": "tsc",
    "start": "node dist/index.js",
    "dev": "ts-node src/index.ts"
  },
  "dependencies": {
    "express": "^4.18.0",
    "typescript": "^5.0.0"
  },
  "devDependencies": {
    "@types/express": "^4.17.0",
    "ts-node": "^10.9.0"
  }
}
""")
        
        print(f"📁 Created test bookstore at: {bookstore_dir}")
        print(f"📄 Files created:")
        for file in bookstore_dir.iterdir():
            print(f"  - {file.name} ({file.stat().st_size} bytes)")
        
        # Test processing with MultiLanguageCodeProcessor
        print("\n🧪 Testing TypeScript Processing...")
        processor = MultiLanguageCodeProcessor(str(bookstore_dir))
        
        # Check file detection
        print("\n1. File Detection Test:")
        for file in bookstore_dir.iterdir():
            if file.is_file():
                parser, language, detected_lang = processor.get_parser_for_file(file)
                print(f"  {file.name}: {detected_lang} (parser: {'✅' if parser else '❌'})")
        
        # Process the directory
        print("\n2. Processing Test:")
        chunks = processor.process_repository(str(bookstore_dir))
        
        print(f"✅ Generated {len(chunks)} chunks")
        
        if len(chunks) > 0:
            print("\n📊 Chunk Analysis:")
            for i, chunk in enumerate(chunks[:3]):  # Show first 3 chunks
                metadata = chunk.get('metadata', {})
                print(f"  Chunk {i+1}:")
                print(f"    File: {metadata.get('relative_path', 'unknown')}")
                print(f"    Language: {metadata.get('language', 'unknown')}")
                print(f"    Type: {metadata.get('type', 'unknown')}")
                print(f"    Lines: {metadata.get('start_line', '?')}-{metadata.get('end_line', '?')}")
                print(f"    Content preview: {chunk.get('content', '')[:100]}...")
                print()
        else:
            print("❌ No chunks generated - investigating why...")
            
            # Check if files are being read
            print("\n3. File Content Check:")
            for file in bookstore_dir.iterdir():
                if file.suffix.lower() in ['.ts', '.tsx']:
                    content = file.read_text()
                    print(f"  {file.name}: {len(content)} characters")
                    if len(content) == 0:
                        print(f"    ⚠️ File is empty!")
                    else:
                        print(f"    ✅ File has content")
        
        return len(chunks) > 0
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)

def test_minimal_typescript():
    """Test with minimal TypeScript content"""
    print("\n🧪 Testing Minimal TypeScript Content...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Create minimal TypeScript file
        ts_file = Path(temp_dir) / "minimal.ts"
        ts_file.write_text("export interface User { name: string; }")
        
        processor = MultiLanguageCodeProcessor(temp_dir)
        chunks = processor.process_repository(temp_dir)
        
        print(f"Minimal TypeScript test: {len(chunks)} chunks generated")
        return len(chunks) > 0
        
    finally:
        shutil.rmtree(temp_dir)

if __name__ == "__main__":
    print("🔧 Bookstore Processing Diagnosis")
    print("=" * 70)
    
    success1 = test_typescript_processing()
    success2 = test_minimal_typescript()
    
    print("\n" + "=" * 70)
    print("📊 DIAGNOSIS RESULTS:")
    print("=" * 70)
    
    if success1 and success2:
        print("✅ TypeScript processing works correctly locally")
        print("🎯 Issue is likely: Bookstore directory on server is empty or has no .ts files")
        print("\n💡 SOLUTION:")
        print("1. Add TypeScript files to the bookstore directory on the server")
        print("2. Or copy the sample files created by this test")
        print("3. Then try 'process bookstore codebase' again")
        
    elif success2 and not success1:
        print("⚠️ Minimal TypeScript works, but complex files have issues")
        print("🎯 Issue might be: File complexity or specific TypeScript features")
        
    else:
        print("❌ TypeScript processing has fundamental issues")
        print("🎯 Issue is likely: TypeScript parser or tree-sitter configuration")
        
    print("\n🚀 Next steps:")
    print("1. Check if bookstore directory on server has any .ts files")
    print("2. If empty, populate with sample TypeScript files")
    print("3. Verify file permissions and accessibility")
    print("4. Try processing again")
