{"version": 3, "targets": {".NETStandard,Version=v2.0": {"Microsoft.CodeAnalysis.FxCopAnalyzers/2.9.8": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.VersionCheckAnalyzer": "[2.9.8]", "Microsoft.CodeQuality.Analyzers": "[2.9.8]", "Microsoft.NetCore.Analyzers": "[2.9.8]", "Microsoft.NetFramework.Analyzers": "[2.9.8]"}, "build": {"build/Microsoft.CodeAnalysis.FxCopAnalyzers.props": {}}}, "Microsoft.CodeAnalysis.VersionCheckAnalyzer/2.9.8": {"type": "package", "build": {"build/Microsoft.CodeAnalysis.VersionCheckAnalyzer.props": {}}}, "Microsoft.CodeQuality.Analyzers/2.9.8": {"type": "package", "build": {"build/Microsoft.CodeQuality.Analyzers.props": {}}}, "Microsoft.NetCore.Analyzers/2.9.8": {"type": "package", "build": {"build/Microsoft.NetCore.Analyzers.props": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NetFramework.Analyzers/2.9.8": {"type": "package", "build": {"build/Microsoft.NetFramework.Analyzers.props": {}}}, "NETStandard.Library/2.0.3": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"build/netstandard2.0/NETStandard.Library.targets": {}}}}}, "libraries": {"Microsoft.CodeAnalysis.FxCopAnalyzers/2.9.8": {"sha512": "FZhO7S+xinFrcRvPyIxPbFgY0Jg3X+KNkawhg7wwoVxnT/ySdoO162dyIMXfQQ6/qCmjNFHmTDXiNlpPNvKNyQ==", "type": "package", "path": "microsoft.codeanalysis.fxcopanalyzers/2.9.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "build/Microsoft.CodeAnalysis.FxCopAnalyzers.props", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.FxCopAnalyzers.md", "documentation/Microsoft.CodeAnalysis.FxCopAnalyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/DesignRulesDefault/.editorconfig", "editorconfig/DesignRulesEnabled/.editorconfig", "editorconfig/DocumentationRulesDefault/.editorconfig", "editorconfig/DocumentationRulesEnabled/.editorconfig", "editorconfig/GlobalizationRulesDefault/.editorconfig", "editorconfig/GlobalizationRulesEnabled/.editorconfig", "editorconfig/InteroperabilityRulesDefault/.editorconfig", "editorconfig/InteroperabilityRulesEnabled/.editorconfig", "editorconfig/MaintainabilityRulesDefault/.editorconfig", "editorconfig/MaintainabilityRulesEnabled/.editorconfig", "editorconfig/NamingRulesDefault/.editorconfig", "editorconfig/NamingRulesEnabled/.editorconfig", "editorconfig/PerformanceRulesDefault/.editorconfig", "editorconfig/PerformanceRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "editorconfig/ReliabilityRulesDefault/.editorconfig", "editorconfig/ReliabilityRulesEnabled/.editorconfig", "editorconfig/SecurityRulesDefault/.editorconfig", "editorconfig/SecurityRulesEnabled/.editorconfig", "editorconfig/UsageRulesDefault/.editorconfig", "editorconfig/UsageRulesEnabled/.editorconfig", "microsoft.codeanalysis.fxcopanalyzers.2.9.8.nupkg.sha512", "microsoft.codeanalysis.fxcopanalyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/DesignRulesDefault.ruleset", "rulesets/DesignRulesEnabled.ruleset", "rulesets/DocumentationRulesDefault.ruleset", "rulesets/DocumentationRulesEnabled.ruleset", "rulesets/GlobalizationRulesDefault.ruleset", "rulesets/GlobalizationRulesEnabled.ruleset", "rulesets/InteroperabilityRulesDefault.ruleset", "rulesets/InteroperabilityRulesEnabled.ruleset", "rulesets/MaintainabilityRulesDefault.ruleset", "rulesets/MaintainabilityRulesEnabled.ruleset", "rulesets/NamingRulesDefault.ruleset", "rulesets/NamingRulesEnabled.ruleset", "rulesets/PerformanceRulesDefault.ruleset", "rulesets/PerformanceRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "rulesets/ReliabilityRulesDefault.ruleset", "rulesets/ReliabilityRulesEnabled.ruleset", "rulesets/SecurityRulesDefault.ruleset", "rulesets/SecurityRulesEnabled.ruleset", "rulesets/UsageRulesDefault.ruleset", "rulesets/UsageRulesEnabled.ruleset", "rulesets/legacy/AllRules.ruleset", "rulesets/legacy/BasicCorrectnessRules.ruleset", "rulesets/legacy/BasicDesignGuidelineRules.ruleset", "rulesets/legacy/ExtendedCorrectnessRules.ruleset", "rulesets/legacy/ExtendedDesignGuidelineRules.ruleset", "rulesets/legacy/GlobalizationRules.ruleset", "rulesets/legacy/ManagedMinimumRules.ruleset", "rulesets/legacy/MinimumRecommendedRules.ruleset", "rulesets/legacy/SecurityRules.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.VersionCheckAnalyzer/2.9.8": {"sha512": "iSjqkECKpagJPjzc7sMjD6rQWSznVYqTfUZ96yR0r0jDzA45TymBof350tlOfGDRvn4OrU/KKJav21/oZDttrA==", "type": "package", "path": "microsoft.codeanalysis.versioncheckanalyzer/2.9.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/Microsoft.CodeAnalysis.VersionCheckAnalyzer.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/de/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/es/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/fr/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/it/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/ja/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/ko/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/pl/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/pt-BR/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/ru/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/tr/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/zh-Hans/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "analyzers/dotnet/zh-Hant/Microsoft.CodeAnalysis.VersionCheckAnalyzer.resources.dll", "build/Microsoft.CodeAnalysis.VersionCheckAnalyzer.props", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.VersionCheckAnalyzer.md", "documentation/Microsoft.CodeAnalysis.VersionCheckAnalyzer.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "editorconfig/ReliabilityRulesDefault/.editorconfig", "editorconfig/ReliabilityRulesEnabled/.editorconfig", "microsoft.codeanalysis.versioncheckanalyzer.2.9.8.nupkg.sha512", "microsoft.codeanalysis.versioncheckanalyzer.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "rulesets/ReliabilityRulesDefault.ruleset", "rulesets/ReliabilityRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeQuality.Analyzers/2.9.8": {"sha512": "KevAiJKuolGKK84jDHMVGJjBAigH/86xyCVtbOzh5avUYzW7jPErdJW708byKKMiVKTwv027mbKCNnnOgNQHsA==", "type": "package", "path": "microsoft.codequality.analyzers/2.9.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Humanizer.dll", "analyzers/dotnet/cs/Microsoft.CodeQuality.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeQuality.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/Humanizer.dll", "analyzers/dotnet/vb/Microsoft.CodeQuality.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeQuality.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeQuality.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeQuality.Analyzers.resources.dll", "build/Microsoft.CodeQuality.Analyzers.props", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeQuality.Analyzers.md", "documentation/Microsoft.CodeQuality.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/DesignRulesDefault/.editorconfig", "editorconfig/DesignRulesEnabled/.editorconfig", "editorconfig/DocumentationRulesDefault/.editorconfig", "editorconfig/DocumentationRulesEnabled/.editorconfig", "editorconfig/MaintainabilityRulesDefault/.editorconfig", "editorconfig/MaintainabilityRulesEnabled/.editorconfig", "editorconfig/NamingRulesDefault/.editorconfig", "editorconfig/NamingRulesEnabled/.editorconfig", "editorconfig/PerformanceRulesDefault/.editorconfig", "editorconfig/PerformanceRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "editorconfig/ReliabilityRulesDefault/.editorconfig", "editorconfig/ReliabilityRulesEnabled/.editorconfig", "editorconfig/SecurityRulesDefault/.editorconfig", "editorconfig/SecurityRulesEnabled/.editorconfig", "editorconfig/UsageRulesDefault/.editorconfig", "editorconfig/UsageRulesEnabled/.editorconfig", "microsoft.codequality.analyzers.2.9.8.nupkg.sha512", "microsoft.codequality.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/DesignRulesDefault.ruleset", "rulesets/DesignRulesEnabled.ruleset", "rulesets/DocumentationRulesDefault.ruleset", "rulesets/DocumentationRulesEnabled.ruleset", "rulesets/MaintainabilityRulesDefault.ruleset", "rulesets/MaintainabilityRulesEnabled.ruleset", "rulesets/NamingRulesDefault.ruleset", "rulesets/NamingRulesEnabled.ruleset", "rulesets/PerformanceRulesDefault.ruleset", "rulesets/PerformanceRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "rulesets/ReliabilityRulesDefault.ruleset", "rulesets/ReliabilityRulesEnabled.ruleset", "rulesets/SecurityRulesDefault.ruleset", "rulesets/SecurityRulesEnabled.ruleset", "rulesets/UsageRulesDefault.ruleset", "rulesets/UsageRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.NetCore.Analyzers/2.9.8": {"sha512": "zbGttCZ8T5wJiBDhgIaFWTrYa/X7zbCnQ76PEu/B4gb64KY+yB9gjkndsKWR+2TbOId76PN7WrSQAn+W7gr5jQ==", "type": "package", "path": "microsoft.netcore.analyzers/2.9.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.NetCore.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.NetCore.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.NetCore.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.NetCore.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.NetCore.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.NetCore.Analyzers.resources.dll", "build/Microsoft.NetCore.Analyzers.props", "documentation/Analyzer Configuration.md", "documentation/Microsoft.NetCore.Analyzers.md", "documentation/Microsoft.NetCore.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/GlobalizationRulesDefault/.editorconfig", "editorconfig/GlobalizationRulesEnabled/.editorconfig", "editorconfig/InteroperabilityRulesDefault/.editorconfig", "editorconfig/InteroperabilityRulesEnabled/.editorconfig", "editorconfig/PerformanceRulesDefault/.editorconfig", "editorconfig/PerformanceRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "editorconfig/ReliabilityRulesDefault/.editorconfig", "editorconfig/ReliabilityRulesEnabled/.editorconfig", "editorconfig/SecurityRulesDefault/.editorconfig", "editorconfig/SecurityRulesEnabled/.editorconfig", "editorconfig/UsageRulesDefault/.editorconfig", "editorconfig/UsageRulesEnabled/.editorconfig", "microsoft.netcore.analyzers.2.9.8.nupkg.sha512", "microsoft.netcore.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/GlobalizationRulesDefault.ruleset", "rulesets/GlobalizationRulesEnabled.ruleset", "rulesets/InteroperabilityRulesDefault.ruleset", "rulesets/InteroperabilityRulesEnabled.ruleset", "rulesets/PerformanceRulesDefault.ruleset", "rulesets/PerformanceRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "rulesets/ReliabilityRulesDefault.ruleset", "rulesets/ReliabilityRulesEnabled.ruleset", "rulesets/SecurityRulesDefault.ruleset", "rulesets/SecurityRulesEnabled.ruleset", "rulesets/UsageRulesDefault.ruleset", "rulesets/UsageRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NetFramework.Analyzers/2.9.8": {"sha512": "8fZYJqeKBW5uuKBact26IeOBogchn5Km85klHqHneRY7Jxp+ERtrw8zJVumNFUVL68pIcf4uKPOY7zfBQ7eY3A==", "type": "package", "path": "microsoft.netframework.analyzers/2.9.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.NetFramework.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.NetFramework.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.NetFramework.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.NetFramework.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.NetFramework.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.NetFramework.Analyzers.resources.dll", "build/Microsoft.NetFramework.Analyzers.props", "documentation/Analyzer Configuration.md", "documentation/Microsoft.NetFramework.Analyzers.md", "documentation/Microsoft.NetFramework.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/DesignRulesDefault/.editorconfig", "editorconfig/DesignRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "editorconfig/SecurityRulesDefault/.editorconfig", "editorconfig/SecurityRulesEnabled/.editorconfig", "microsoft.netframework.analyzers.2.9.8.nupkg.sha512", "microsoft.netframework.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/DesignRulesDefault.ruleset", "rulesets/DesignRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "rulesets/SecurityRulesDefault.ruleset", "rulesets/SecurityRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "NETStandard.Library/2.0.3": {"sha512": "st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "type": "package", "path": "netstandard.library/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/netstandard2.0/NETStandard.Library.targets", "build/netstandard2.0/ref/Microsoft.Win32.Primitives.dll", "build/netstandard2.0/ref/System.AppContext.dll", "build/netstandard2.0/ref/System.Collections.Concurrent.dll", "build/netstandard2.0/ref/System.Collections.NonGeneric.dll", "build/netstandard2.0/ref/System.Collections.Specialized.dll", "build/netstandard2.0/ref/System.Collections.dll", "build/netstandard2.0/ref/System.ComponentModel.Composition.dll", "build/netstandard2.0/ref/System.ComponentModel.EventBasedAsync.dll", "build/netstandard2.0/ref/System.ComponentModel.Primitives.dll", "build/netstandard2.0/ref/System.ComponentModel.TypeConverter.dll", "build/netstandard2.0/ref/System.ComponentModel.dll", "build/netstandard2.0/ref/System.Console.dll", "build/netstandard2.0/ref/System.Core.dll", "build/netstandard2.0/ref/System.Data.Common.dll", "build/netstandard2.0/ref/System.Data.dll", "build/netstandard2.0/ref/System.Diagnostics.Contracts.dll", "build/netstandard2.0/ref/System.Diagnostics.Debug.dll", "build/netstandard2.0/ref/System.Diagnostics.FileVersionInfo.dll", "build/netstandard2.0/ref/System.Diagnostics.Process.dll", "build/netstandard2.0/ref/System.Diagnostics.StackTrace.dll", "build/netstandard2.0/ref/System.Diagnostics.TextWriterTraceListener.dll", "build/netstandard2.0/ref/System.Diagnostics.Tools.dll", "build/netstandard2.0/ref/System.Diagnostics.TraceSource.dll", "build/netstandard2.0/ref/System.Diagnostics.Tracing.dll", "build/netstandard2.0/ref/System.Drawing.Primitives.dll", "build/netstandard2.0/ref/System.Drawing.dll", "build/netstandard2.0/ref/System.Dynamic.Runtime.dll", "build/netstandard2.0/ref/System.Globalization.Calendars.dll", "build/netstandard2.0/ref/System.Globalization.Extensions.dll", "build/netstandard2.0/ref/System.Globalization.dll", "build/netstandard2.0/ref/System.IO.Compression.FileSystem.dll", "build/netstandard2.0/ref/System.IO.Compression.ZipFile.dll", "build/netstandard2.0/ref/System.IO.Compression.dll", "build/netstandard2.0/ref/System.IO.FileSystem.DriveInfo.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Primitives.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Watcher.dll", "build/netstandard2.0/ref/System.IO.FileSystem.dll", "build/netstandard2.0/ref/System.IO.IsolatedStorage.dll", "build/netstandard2.0/ref/System.IO.MemoryMappedFiles.dll", "build/netstandard2.0/ref/System.IO.Pipes.dll", "build/netstandard2.0/ref/System.IO.UnmanagedMemoryStream.dll", "build/netstandard2.0/ref/System.IO.dll", "build/netstandard2.0/ref/System.Linq.Expressions.dll", "build/netstandard2.0/ref/System.Linq.Parallel.dll", "build/netstandard2.0/ref/System.Linq.Queryable.dll", "build/netstandard2.0/ref/System.Linq.dll", "build/netstandard2.0/ref/System.Net.Http.dll", "build/netstandard2.0/ref/System.Net.NameResolution.dll", "build/netstandard2.0/ref/System.Net.NetworkInformation.dll", "build/netstandard2.0/ref/System.Net.Ping.dll", "build/netstandard2.0/ref/System.Net.Primitives.dll", "build/netstandard2.0/ref/System.Net.Requests.dll", "build/netstandard2.0/ref/System.Net.Security.dll", "build/netstandard2.0/ref/System.Net.Sockets.dll", "build/netstandard2.0/ref/System.Net.WebHeaderCollection.dll", "build/netstandard2.0/ref/System.Net.WebSockets.Client.dll", "build/netstandard2.0/ref/System.Net.WebSockets.dll", "build/netstandard2.0/ref/System.Net.dll", "build/netstandard2.0/ref/System.Numerics.dll", "build/netstandard2.0/ref/System.ObjectModel.dll", "build/netstandard2.0/ref/System.Reflection.Extensions.dll", "build/netstandard2.0/ref/System.Reflection.Primitives.dll", "build/netstandard2.0/ref/System.Reflection.dll", "build/netstandard2.0/ref/System.Resources.Reader.dll", "build/netstandard2.0/ref/System.Resources.ResourceManager.dll", "build/netstandard2.0/ref/System.Resources.Writer.dll", "build/netstandard2.0/ref/System.Runtime.CompilerServices.VisualC.dll", "build/netstandard2.0/ref/System.Runtime.Extensions.dll", "build/netstandard2.0/ref/System.Runtime.Handles.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.RuntimeInformation.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.dll", "build/netstandard2.0/ref/System.Runtime.Numerics.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Formatters.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Json.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Primitives.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Xml.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.dll", "build/netstandard2.0/ref/System.Runtime.dll", "build/netstandard2.0/ref/System.Security.Claims.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Algorithms.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Csp.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Encoding.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Primitives.dll", "build/netstandard2.0/ref/System.Security.Cryptography.X509Certificates.dll", "build/netstandard2.0/ref/System.Security.Principal.dll", "build/netstandard2.0/ref/System.Security.SecureString.dll", "build/netstandard2.0/ref/System.ServiceModel.Web.dll", "build/netstandard2.0/ref/System.Text.Encoding.Extensions.dll", "build/netstandard2.0/ref/System.Text.Encoding.dll", "build/netstandard2.0/ref/System.Text.RegularExpressions.dll", "build/netstandard2.0/ref/System.Threading.Overlapped.dll", "build/netstandard2.0/ref/System.Threading.Tasks.Parallel.dll", "build/netstandard2.0/ref/System.Threading.Tasks.dll", "build/netstandard2.0/ref/System.Threading.Thread.dll", "build/netstandard2.0/ref/System.Threading.ThreadPool.dll", "build/netstandard2.0/ref/System.Threading.Timer.dll", "build/netstandard2.0/ref/System.Threading.dll", "build/netstandard2.0/ref/System.Transactions.dll", "build/netstandard2.0/ref/System.ValueTuple.dll", "build/netstandard2.0/ref/System.Web.dll", "build/netstandard2.0/ref/System.Windows.dll", "build/netstandard2.0/ref/System.Xml.Linq.dll", "build/netstandard2.0/ref/System.Xml.ReaderWriter.dll", "build/netstandard2.0/ref/System.Xml.Serialization.dll", "build/netstandard2.0/ref/System.Xml.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.dll", "build/netstandard2.0/ref/System.Xml.XmlDocument.dll", "build/netstandard2.0/ref/System.Xml.XmlSerializer.dll", "build/netstandard2.0/ref/System.Xml.dll", "build/netstandard2.0/ref/System.dll", "build/netstandard2.0/ref/mscorlib.dll", "build/netstandard2.0/ref/netstandard.dll", "build/netstandard2.0/ref/netstandard.xml", "lib/netstandard1.0/_._", "netstandard.library.2.0.3.nupkg.sha512", "netstandard.library.nuspec"]}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.0": ["Microsoft.CodeAnalysis.FxCopAnalyzers >= 2.9.8", "NETStandard.Library >= 2.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.1", "restore": {"projectUniqueName": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\Essenbee.Z80.csproj", "projectName": "Essenbee.Z80", "projectPath": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\Essenbee.Z80.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.CodeAnalysis.FxCopAnalyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.9.8, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.314\\RuntimeIdentifierGraph.json"}}}}