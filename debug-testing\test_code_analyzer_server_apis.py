#!/usr/bin/env python3
"""
Comprehensive Code Analysis Server API Testing Tool

This script tests all available endpoints on the Code Analysis server to understand
what APIs are available and working properly.
"""

import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional

# Code Analysis Server Configuration
CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"
TIMEOUT = 60  # General timeout increased to 60 seconds
AI_TIMEOUT = 120  # Special timeout for AI operations (2 minutes)

class CodeAnalysisServerAPITester:
    def __init__(self, server_url: str = CODE_ANALYZER_SERVER_URL):
        self.server_url = server_url
        self.results: Dict[str, Dict[str, Any]] = {}
        
    def test_endpoint(self, endpoint: str, method: str = "GET", payload: Optional[Dict] = None, description: str = "", timeout: Optional[int] = None) -> Dict[str, Any]:
        """Test a single endpoint and return results"""
        url = f"{self.server_url}{endpoint}"
        
        print(f"\n{'='*60}")
        print(f"🧪 TESTING: {method} {endpoint}")
        print(f"📝 Description: {description}")
        print(f"🔗 URL: {url}")
        if payload:
            print(f"📦 Payload: {json.dumps(payload, indent=2)}")
        print(f"{'='*60}")
        
        # Use custom timeout if provided, otherwise use default
        request_timeout = timeout if timeout is not None else TIMEOUT

        try:
            if method.upper() == "GET":
                response = requests.get(url, timeout=request_timeout)
            elif method.upper() == "POST":
                response = requests.post(url, json=payload, timeout=request_timeout)
            else:
                return {"success": False, "error": f"Unsupported method: {method}"}
            
            # Parse response
            try:
                response_data = response.json()
            except (ValueError, requests.exceptions.JSONDecodeError):
                response_data = response.text
            
            result = {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": response_data,
                "url": url,
                "method": method,
                "payload": payload
            }
            
            if result["success"]:
                print(f"✅ SUCCESS: {response.status_code}")
                print(f"📄 Response: {json.dumps(response_data, indent=2)[:500]}...")
            else:
                print(f"❌ FAILED: {response.status_code}")
                print(f"📄 Response: {response_data}")
                
        except Exception as e:
            result = {
                "success": False,
                "error": str(e),
                "url": url,
                "method": method,
                "payload": payload
            }
            print(f"❌ ERROR: {str(e)}")
        
        return result
    
    def test_all_endpoints(self):
        """Test all known Code Analysis server endpoints"""
        print("🚀 Starting Comprehensive Code Analysis Server API Testing...")
        print(f"🔗 Server: {self.server_url}")
        print(f"🕒 Started: {datetime.now()}")
        
        # 1. Health/Status Endpoints
        print(f"\n{'#'*80}")
        print("# 1. HEALTH & STATUS ENDPOINTS")
        print(f"{'#'*80}")
        
        self.results["health"] = self.test_endpoint(
            "/health", "GET", 
            description="Server health check"
        )
        
        self.results["status"] = self.test_endpoint(
            "/status", "GET",
            description="Server status information (requires server version 3.0.0+)"
        )
        
        # 2. Codebase Management Endpoints
        print(f"\n{'#'*80}")
        print("# 2. CODEBASE MANAGEMENT ENDPOINTS")
        print(f"{'#'*80}")
        
        self.results["list_codebases"] = self.test_endpoint(
            "/tools/list_codebases", "POST", {},
            description="List all available codebases"
        )
        
        self.results["get_code_stats"] = self.test_endpoint(
            "/tools/get_code_stats", "POST", {"codebase_name": "utils"},
            description="Get statistics for utils codebase"
        )
        
        # 3. Search & Analysis Endpoints
        print(f"\n{'#'*80}")
        print("# 3. SEARCH & ANALYSIS ENDPOINTS")
        print(f"{'#'*80}")
        
        self.results["search_code"] = self.test_endpoint(
            "/tools/search_code", "POST", {
                "query": "memory management",
                "codebase_name": "utils",
                "n_results": 3
            },
            description="Search for code related to memory management"
        )
        
        self.results["ask_about_code"] = self.test_endpoint(
            "/tools/ask_about_code", "POST", {
                "question": "How does memory allocation work?",
                "codebase_name": "utils"
            },
            description="Ask AI question about code",
            timeout=AI_TIMEOUT
        )

        self.results["raw_search"] = self.test_endpoint(
            "/search", "POST", {
                "query": "memory allocation",
                "n_results": 3
            },
            description="Raw search endpoint (used by tool)"
        )
        
        # 4. Processing Endpoints
        print(f"\n{'#'*80}")
        print("# 4. PROCESSING ENDPOINTS")
        print(f"{'#'*80}")
        
        # Note: Don't actually process, just test if endpoint exists
        self.results["process_codebase_check"] = self.test_endpoint(
            "/tools/process_codebase", "POST", {
                "codebase_name": "test_check_only",
                "exclude_dirs": ["build", "test"]
            },
            description="Check if process_codebase endpoint exists (expect failure)"
        )
        
        # 5. Discovery Endpoints
        print(f"\n{'#'*80}")
        print("# 5. DISCOVERY ENDPOINTS")
        print(f"{'#'*80}")
        
        self.results["root"] = self.test_endpoint(
            "/", "GET",
            description="Root endpoint"
        )
        
        self.results["docs"] = self.test_endpoint(
            "/docs", "GET",
            description="API documentation"
        )
        
        self.results["openapi"] = self.test_endpoint(
            "/openapi.json", "GET",
            description="OpenAPI specification"
        )
        
    def generate_report(self):
        """Generate a comprehensive test report"""
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE TEST REPORT")
        print(f"{'='*80}")
        
        successful = sum(1 for result in self.results.values() if result.get("success", False))
        total = len(self.results)
        
        print(f"📈 Overall Success Rate: {successful}/{total} ({successful/total*100:.1f}%)")
        print(f"🕒 Completed: {datetime.now()}")
        
        print(f"\n{'='*60}")
        print("✅ SUCCESSFUL ENDPOINTS:")
        print(f"{'='*60}")
        for name, result in self.results.items():
            if result.get("success", False):
                print(f"✅ {name}: {result['method']} {result['url'].replace(self.server_url, '')}")
        
        print(f"\n{'='*60}")
        print("❌ FAILED ENDPOINTS:")
        print(f"{'='*60}")
        for name, result in self.results.items():
            if not result.get("success", False):
                error = result.get("error", f"HTTP {result.get('status_code', 'Unknown')}")
                print(f"❌ {name}: {error}")
        
        # Save detailed results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ca_server_api_test_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to: {filename}")
        
        return self.results

def main():
    """Run comprehensive API testing"""
    tester = CodeAnalysisServerAPITester()
    tester.test_all_endpoints()
    results = tester.generate_report()
    
    # Print key findings
    print(f"\n{'='*80}")
    print("🔍 KEY FINDINGS:")
    print(f"{'='*80}")

    # Check server version from root endpoint
    server_version = "unknown"
    if results.get("root", {}).get("success"):
        root_response = results["root"].get("response", {})
        server_version = root_response.get("version", "unknown")
        print(f"🏷️  Server Version: {server_version}")

    if results.get("health", {}).get("success"):
        print("✅ Server is healthy and responding")
    else:
        print("❌ Server health check failed - server may be down")
    
    if results.get("list_codebases", {}).get("success"):
        print("✅ Codebase listing works")
    else:
        print("❌ Cannot list codebases - core functionality broken")
    
    if results.get("search_code", {}).get("success"):
        print("✅ Code search functionality works")
    else:
        print("❌ Code search broken - main feature not working")
    
    if results.get("ask_about_code", {}).get("success"):
        print("✅ AI code analysis works")
    else:
        print("❌ AI code analysis broken")

    # Special handling for status endpoint
    if not results.get("status", {}).get("success"):
        status_code = results.get("status", {}).get("status_code", 0)
        if status_code == 404 and server_version == "2.1.0":
            print("⚠️  /status endpoint missing (expected for version 2.1.0)")
            print("   Server needs restart to pick up version 3.1.0 code")
        else:
            print("❌ Status endpoint broken")

if __name__ == "__main__":
    main()
