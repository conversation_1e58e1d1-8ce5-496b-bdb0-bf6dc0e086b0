#!/usr/bin/env python3
"""
Debug script to check what files are being detected in a Rust project structure
"""

import os
from pathlib import Path
from code_preprocessor import MultiLanguageCodeProcessor

def debug_rust_file_detection():
    """Debug file detection for Rust project structure"""
    
    print("🔍 Debugging Rust File Detection")
    print("=" * 60)
    
    # Create a test directory structure similar to rust-starter-master
    import tempfile
    import shutil
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Create rust project structure
        rust_project = Path(temp_dir) / "rust-starter-master"
        rust_project.mkdir()
        
        # Create Cargo.toml
        (rust_project / "Cargo.toml").write_text("""
[package]
name = "rust-starter"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = "1.0"
""")
        
        # Create README.md
        (rust_project / "README.md").write_text("# Rust Starter Project")
        
        # Create src directory structure
        src_dir = rust_project / "src"
        src_dir.mkdir()
        
        # Create main.rs
        (src_dir / "main.rs").write_text("""
use std::collections::HashMap;

fn main() {
    println!("Hello, world!");
}
""")
        
        # Create lib.rs
        (src_dir / "lib.rs").write_text("""
pub mod foo;
pub mod module1;

pub use foo::*;
""")
        
        # Create foo module
        foo_dir = src_dir / "foo"
        foo_dir.mkdir()
        (foo_dir / "mod.rs").write_text("pub mod bar;")
        (foo_dir / "bar.rs").write_text("pub fn hello() { println!(\"Hello from bar!\"); }")
        
        # Create module1
        module1_dir = src_dir / "module1"
        module1_dir.mkdir()
        (module1_dir / "mod.rs").write_text("pub mod blah;")
        (module1_dir / "blah.rs").write_text("pub fn test() { println!(\"Test from module1\"); }")
        
        print(f"📁 Created test Rust project at: {rust_project}")
        
        # Show the directory structure
        print("\n📂 Directory Structure:")
        for root, dirs, files in os.walk(rust_project):
            level = root.replace(str(rust_project), '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}{file}")
        
        # Test file detection
        print(f"\n🔍 Testing File Detection on: {rust_project}")
        processor = MultiLanguageCodeProcessor(str(rust_project))
        
        # Manually check what files are found
        print("\n1. Manual File Walk:")
        source_extensions = {'.rs', '.toml'}  # Focus on Rust files
        found_files = []
        
        for root, dirs, files in os.walk(rust_project):
            root_path = Path(root)
            print(f"  Scanning: {root_path}")
            
            for file in files:
                filepath = root_path / file
                if filepath.suffix.lower() in source_extensions:
                    found_files.append(filepath)
                    print(f"    ✅ Found: {filepath.relative_to(rust_project)} ({filepath.suffix})")
                else:
                    print(f"    ⏭️  Skipped: {filepath.relative_to(rust_project)} ({filepath.suffix})")
        
        print(f"\n📊 Total files found: {len(found_files)}")
        
        # Test with processor
        print("\n2. Processor File Detection:")
        try:
            chunks = processor.process_repository(str(rust_project))
            print(f"  ✅ Processor generated: {len(chunks)} chunks")
            
            if len(chunks) > 0:
                print("\n  📋 Chunk Details:")
                for i, chunk in enumerate(chunks):
                    metadata = chunk.get('metadata', {})
                    file_path = metadata.get('relative_path', 'unknown')
                    language = metadata.get('language', 'unknown')
                    chunk_type = metadata.get('type', 'unknown')
                    print(f"    {i+1}. {file_path} ({language}, {chunk_type})")
            else:
                print("  ❌ No chunks generated")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
        
        # Test individual file detection
        print("\n3. Individual File Language Detection:")
        for file_path in found_files:
            try:
                parser, language, detected_lang = processor.get_parser_for_file(file_path)
                parser_status = "✅" if parser else "❌"
                print(f"  {file_path.name}: {detected_lang} (parser: {parser_status})")
            except Exception as e:
                print(f"  {file_path.name}: Error - {e}")
        
        return len(found_files), len(chunks) if 'chunks' in locals() else 0
        
    finally:
        shutil.rmtree(temp_dir)

def check_exclude_logic():
    """Check if 'src' directory is being excluded"""
    
    print("\n🔍 Checking Exclude Logic")
    print("=" * 40)
    
    # Default exclude dirs from the processor
    exclude_dirs = {'.git', '.svn', 'build', 'dist', '__pycache__', 'node_modules', 
                   'bin', 'obj', '.vs', '.vscode', '.idea', 'packages'}
    
    test_dirs = ['src', 'lib', 'target', 'build', 'node_modules', '.git']
    
    def should_exclude_dir(dir_name):
        return any(excluded in dir_name.lower() for excluded in exclude_dirs)
    
    for dir_name in test_dirs:
        excluded = should_exclude_dir(dir_name)
        status = "❌ EXCLUDED" if excluded else "✅ INCLUDED"
        print(f"  {dir_name}: {status}")

if __name__ == "__main__":
    print("🦀 Rust File Detection Debug")
    print("=" * 70)
    
    files_found, chunks_generated = debug_rust_file_detection()
    check_exclude_logic()
    
    print("\n" + "=" * 70)
    print("📊 DEBUG SUMMARY:")
    print("=" * 70)
    
    print(f"Files Found: {files_found}")
    print(f"Chunks Generated: {chunks_generated}")
    
    if files_found > 0 and chunks_generated > 0:
        print("✅ File detection and processing working correctly")
        print("🎯 Issue might be with the actual rust-starter-master directory structure")
        print("💡 Check if files are in expected locations on the server")
    elif files_found > 0 and chunks_generated == 0:
        print("⚠️ Files found but no chunks generated")
        print("🎯 Issue is in the processing logic (header detection)")
    else:
        print("❌ No files found")
        print("🎯 Issue is in the file detection logic")
    
    print("\n🔧 RECOMMENDATIONS:")
    print("1. Verify rust-starter-master has files in src/ directory")
    print("2. Check if file extensions are correct (.rs)")
    print("3. Ensure no permission issues on server")
    print("4. Try processing again after confirming file structure")
