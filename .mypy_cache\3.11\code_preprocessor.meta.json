{"data_mtime": 1751247377, "dep_lines": [2, 3, 4, 5, 6, 7, 8, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "re", "pathlib", "<PERSON><PERSON><PERSON>", "json", "typing", "datetime", "tree_sitter_language_pack", "builtins", "_collections_abc", "_frozen_importlib", "_hashlib", "_io", "_typeshed", "abc", "enum", "io", "json.encoder", "tree_sitter", "typing_extensions"], "hash": "98a53a66cd9d4e812f9100ef686e71af7756d802", "id": "code_preprocessor", "ignore_all": false, "interface_hash": "ebe2dd221aedccf41cf15cc247c66838bed6a731", "mtime": 1751294777, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\code_preprocessor.py", "plugin_data": null, "size": 101771, "suppressed": [], "version_id": "1.15.0"}