#!/usr/bin/env python3
"""
Quick status check to see current tool state
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def quick_test():
    """Quick test to see current tool behavior"""
    print("🔍 Quick Status Check")
    print("=" * 40)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test the current behavior
    print("Testing: 'select codebase utils'")
    print("-" * 30)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select codebase utils"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"Response ({len(content)} chars):")
            print(f"'{content}'")
            print()
            
            # Analyze the response
            if "Selected Enhanced Codebase: utils" in content:
                print("🎉 STATUS: TOOL IS WORKING CORRECTLY!")
                print("✅ The tool has been successfully updated.")
                
            elif "The codebase you are interested in is utils" in content:
                print("⚠️ STATUS: TOOL NEEDS UPDATE")
                print("❌ The tool is responding but using old/generic logic.")
                print("📋 ACTION NEEDED: Update tool code in OpenWebUI interface.")
                
            elif "popular utility libraries" in content or "popular codebase utilities" in content:
                print("❌ STATUS: TOOL NOT WORKING")
                print("❌ Getting generic response - tool may not be properly installed.")
                print("📋 ACTION NEEDED: Check tool installation and model assignment.")
                
            else:
                print("❓ STATUS: UNCLEAR")
                print("❓ Unexpected response format.")
                print("📋 ACTION NEEDED: Manual investigation required.")
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            print("📋 ACTION NEEDED: Check OpenWebUI server and authentication.")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        print("📋 ACTION NEEDED: Check network connection and server status.")

if __name__ == "__main__":
    quick_test()
