#!/usr/bin/env python3
"""
<PERSON>ript to verify that the 27-language fix is working on the server
"""

import requests
import json
import time

def check_server_languages():
    """Check if the server supports 27 languages"""
    try:
        response = requests.get("http://home-ai-server.local:5002/health", timeout=10)
        data = response.json()
        
        print("🔍 Server Language Support Check")
        print("=" * 50)
        print(f"Server Version: {data.get('version', 'unknown')}")
        
        supported_languages = data.get('supported_languages', [])
        print(f"Supported Languages: {len(supported_languages)}")
        
        if len(supported_languages) >= 27:
            print("✅ SUCCESS: Server supports 27+ languages!")
            print("Languages:", supported_languages)
            return True
        else:
            print(f"❌ ISSUE: Server only supports {len(supported_languages)} languages")
            print("Current languages:", supported_languages)
            print("\n🔧 Expected languages should include:")
            expected = [
                "C", "C++", "Python", "C#", "JavaScript", "TypeScript", 
                "Rust", "Java", "Go", "SQL", "TCL", "Verilog", "Bash",
                "CommonLisp", "EmacsLisp", "Scheme", "Lua", "Make",
                "JSON", "YAML", "XML", "PHP", "Perl", "Markdown", 
                "HTML", "Fortran", "VHDL"
            ]
            print(expected)
            return False
            
    except Exception as e:
        print(f"❌ ERROR: Cannot connect to server: {e}")
        return False

def test_typescript_processing():
    """Test if TypeScript files can be processed"""
    try:
        # Try to process a simple TypeScript snippet
        test_data = {
            "codebase_name": "bookstore",
            "source_path": "./source_code/bookstore"
        }
        
        response = requests.post(
            "http://home-ai-server.local:5002/tools/process_codebase",
            json=test_data,
            timeout=30
        )
        
        result = response.json()
        print("\n🧪 TypeScript Processing Test")
        print("=" * 50)
        
        if "division by zero" in str(result).lower():
            print("❌ ISSUE: Still getting division by zero error")
            return False
        elif "Collection bookstore does not exist" in str(result):
            print("⚠️  INFO: Bookstore collection doesn't exist yet (expected)")
            print("✅ SUCCESS: No division by zero error!")
            return True
        elif "success" in str(result).lower():
            print("✅ SUCCESS: TypeScript processing working!")
            return True
        else:
            print(f"⚠️  UNKNOWN: Unexpected response: {result}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: Cannot test TypeScript processing: {e}")
        return False

def main():
    print("🚀 Verifying 27-Language Support Fix")
    print("=" * 60)
    
    # Test 1: Check language support
    languages_ok = check_server_languages()
    
    # Test 2: Check TypeScript processing
    typescript_ok = test_typescript_processing()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    if languages_ok and typescript_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Server supports 27 languages")
        print("✅ TypeScript processing works")
        print("✅ Division by zero error fixed")
        print("\n🚀 You can now successfully index the bookstore codebase!")
        
    elif languages_ok:
        print("⚠️  PARTIAL SUCCESS")
        print("✅ Server supports 27 languages")
        print("❌ TypeScript processing needs attention")
        print("\n🔧 Try rebuilding the Docker container")
        
    else:
        print("❌ TESTS FAILED")
        print("❌ Server still using old code (4 languages)")
        print("\n🔧 REQUIRED ACTION:")
        print("1. Rebuild Docker container with updated code")
        print("2. Restart the code-analyzer-server service")
        print("3. Run this script again to verify")

if __name__ == "__main__":
    main()
