{"data_mtime": 1751211861, "dep_lines": [9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "json", "datetime", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "http", "http.cookiejar", "io", "json.decoder", "json.encoder", "os", "requests.auth", "requests.exceptions", "requests.models", "typing_extensions"], "hash": "61f1ed5f69c02b787e320a2e04339bf319629795", "id": "test_code_analyzer_server_apis", "ignore_all": false, "interface_hash": "839ea0a09c7628d685c7cb902afc6c925653dce6", "mtime": 1751295641, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\debug-testing\\test_code_analyzer_server_apis.py", "plugin_data": null, "size": 9793, "suppressed": [], "version_id": "1.15.0"}